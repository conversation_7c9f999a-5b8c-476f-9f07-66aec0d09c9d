"# aflamNET - منصة مشاهدة الأفلام

منصة aflamNET هي موقع لمشاهدة الأفلام والفيديوهات عبر الإنترنت، مبنية باستخدام تقنيات MERN Stack (MongoDB, Express, React, Node.js).

## المميزات

- 🎬 مشاهدة الأفلام والفيديوهات من Google Drive
- 👤 نظام تسجيل دخول وإدارة حسابات المستخدمين
- 🔍 البحث عن الأفلام حسب التصنيف
- 📱 تصميم متجاوب يعمل على جميع الأجهزة
- 📤 إمكانية رفع الفيديوهات الخاصة بك
- 👍 إمكانية الإعجاب بالفيديوهات والتعليق عليها
- 🔔 نظام اشتراك في القنوات

## التقنيات المستخدمة

### الواجهة الأمامية (Frontend)
- React.js
- Redux Toolkit
- Vite
- CSS

### الخادم الخلفي (Backend)
- Node.js
- Express.js
- MongoDB
- JWT للمصادقة

## كيفية التشغيل محلياً

### متطلبات النظام
- Node.js (الإصدار 14 أو أحدث)
- MongoDB

### خطوات التثبيت

1. استنساخ المستودع:
```
git clone https://github.com/Moscow2004/film.git
cd film
```

2. تثبيت اعتماديات الخادم الخلفي:
```
cd backend
npm install
```

3. تثبيت اعتماديات الواجهة الأمامية:
```
cd ../frontend
npm install
```

4. تشغيل الخادم الخلفي:
```
cd ../backend
node server.js
```

5. تشغيل الواجهة الأمامية:
```
cd ../frontend
npm run dev
```

6. افتح المتصفح على العنوان:
```
http://localhost:5173
```

## الترخيص

هذا المشروع مرخص بموجب [MIT License](LICENSE)."
