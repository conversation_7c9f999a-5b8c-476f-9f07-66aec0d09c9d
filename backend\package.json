{"name": "aflamnet-backend", "version": "1.0.0", "description": "Backend for aflamNET streaming platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "engines": {"node": ">=14.0.0"}, "dependencies": {"bcrypt": "^5.1.0", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.1.4", "ejs": "^3.1.9", "express": "^4.18.2", "hbs": "^4.2.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.2.4", "nodemailer": "^6.9.4", "nodemon": "^3.0.1", "react-top-loading-bar": "^2.3.1", "validator": "^13.9.0"}}