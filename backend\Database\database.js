require("dotenv").config()
const mongoose = require("mongoose");

console.log("Connecting to MongoDB...");
mongoose
  .connect(
    "mongodb+srv://aryaspubg12345:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0",
    {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    }
  )
  .then(() => {
    console.log("Connected to MongoDB successfully");
    // Test the connection by listing all collections
    mongoose.connection.db.listCollections().toArray((err, collections) => {
      if (err) {
        console.log("Error listing collections:", err);
      } else {
        console.log("Available collections:", collections.map(c => c.name));
      }
    });
  })
  .catch((err) => console.log("MongoDB connection error:", err)); //If not connected to DB