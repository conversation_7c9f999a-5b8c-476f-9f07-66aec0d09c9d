import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useSelector } from "react-redux";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import DeleteIcon from "@mui/icons-material/Delete";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

function ChannelVideos(prop) {
  // const backendURL = "https://youtube-clone-mern-backend.vercel.app"
  const backendURL = "http://localhost:3000"
  const [myVideos, setMyVideos] = useState([]);
  const [videosort, setVideoSort] = useState();
  const [loading, setLoading] = useState(true);
  const [showDiv, setShowDiv] = useState(false);
  const [menuOpen, setMenuOpen] = useState(null); // لتتبع أي فيديو تم فتح قائمته
  const [theme, setTheme] = useState(() => {
    const Dark = localStorage.getItem("Dark");
    return Dark ? JSON.parse(Dark) : true;
  });
  const User = useSelector((state) => state.user.user);
  const { user } = User;
  const navigate = useNavigate();

  // إشعارات نجاح أو فشل العمليات
  const deleteSuccessNotify = () =>
    toast.success("تم حذف الفيديو بنجاح!", {
      position: "bottom-center",
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const deleteErrorNotify = () =>
    toast.error("حدث خطأ أثناء حذف الفيديو!", {
      position: "bottom-center",
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    function handleResize() {
      setShowDiv(window.innerWidth <= 600);
    }

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const getUserVideos = async () => {
      try {
        if (user?.email === prop?.newmail) {
          const response = await fetch(
            `${backendURL}/getuservideos/${user?.email}`
          );
          const myvideos = await response.json();
          setMyVideos(myvideos);
        } else {
          const response = await fetch(
            `${backendURL}/getuservideos/${prop?.newmail}`
          );
          const myvideos = await response.json();
          setMyVideos(myvideos);
        }
      } catch (error) {
        // console.log(error.message);
      }
    };

    getUserVideos();
  }, [user?.email, prop?.newmail]);

  const updateViews = async (id) => {
    try {
      const response = await fetch(
        `${backendURL}/updateview/${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      await response.json();
    } catch (error) {
      // console.log(error.message);
    }
  };

  // وظيفة حذف الفيديو
  const deleteVideo = async (videoId, event) => {
    // منع انتشار الحدث لتجنب فتح الفيديو
    event.stopPropagation();

    // إغلاق القائمة
    setMenuOpen(null);

    // تأكيد الحذف
    if (!window.confirm("هل أنت متأكد من حذف هذا الفيديو؟")) {
      return;
    }

    try {
      const response = await fetch(
        `${backendURL}/deletevideo/${user?.email}/${videoId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const result = await response.json();

      if (response.ok) {
        // تحديث قائمة الفيديوهات بعد الحذف
        setMyVideos(myVideos.filter(video => video._id !== videoId));
        deleteSuccessNotify();
      } else {
        console.error("Error deleting video:", result.message);
        deleteErrorNotify();
      }
    } catch (error) {
      console.error("Error deleting video:", error);
      deleteErrorNotify();
    }
  };

  // وظيفة لفتح/إغلاق قائمة الخيارات
  const toggleMenu = (videoId, event) => {
    // منع انتشار الحدث لتجنب فتح الفيديو
    event.stopPropagation();

    // إذا كانت القائمة مفتوحة لهذا الفيديو، أغلقها
    // وإلا افتحها لهذا الفيديو وأغلق أي قائمة أخرى
    setMenuOpen(menuOpen === videoId ? null : videoId);
  };

  useEffect(() => {
    const sortVideos = () => {
      switch (videosort) {
        case "Latest":
          setMyVideos((prevVideos) =>
            [...prevVideos].sort(
              (a, b) => new Date(b.uploaded_date) - new Date(a.uploaded_date)
            )
          );
          break;
        case "Popular":
          setMyVideos((prevVideos) =>
            [...prevVideos].sort((a, b) => b.views - a.views)
          );
          break;
        case "Oldest":
          setMyVideos((prevVideos) =>
            [...prevVideos].sort(
              (a, b) => new Date(a.uploaded_date) - new Date(b.uploaded_date)
            )
          );
          break;
        default:
          break;
      }
    };

    sortVideos();
  }, [videosort]);

  return (
    <>
      <div className="allvideo-sectionn">
        <div className="video-sorting">
          <button
            className={
              videosort === "Latest"
                ? `latest-video ${theme ? "" : "btn-light-mode"} active${
                    theme ? "" : "-light"
                  }`
                : `latest-video ${theme ? "" : "btn-light-mode"}`
            }
            onClick={() => {
              setVideoSort("Latest");
            }}
          >
            Latest
          </button>
          <button
            className={
              videosort === "Popular"
                ? `Popular-video ${theme ? "" : "btn-light-mode"} active${
                    theme ? "" : "-light"
                  }`
                : `Popular-video ${theme ? "" : "btn-light-mode"}`
            }
            onClick={() => {
              setVideoSort("Popular");
            }}
          >
            Popular
          </button>
          <button
            className={
              videosort === "Oldest"
                ? `Oldest-video ${theme ? "" : "btn-light-mode"} active${
                    theme ? "" : "-light"
                  }`
                : `Oldest-video ${theme ? "" : "btn-light-mode"}`
            }
            onClick={() => {
              setVideoSort("Oldest");
            }}
          >
            Oldest
          </button>
        </div>
        <SkeletonTheme
          baseColor={theme ? "#353535" : "#aaaaaa"}
          highlightColor={theme ? "#444" : "#b6b6b6"}
        >
          <div
            className="sk-uploadedvideos-sectionall"
            style={loading === true ? { display: "grid" } : { display: "none" }}
          >
            {myVideos.length > 0 &&
              myVideos.map((element, index) => {
                return (
                  <div
                    className="uploaded-video-contents sk-uploadcontent"
                    key={index}
                    style={{
                      display:
                        element.visibility === "Public" ? "block" : "none",
                    }}
                  >
                    <Skeleton
                      count={1}
                      width={300}
                      height={169}
                      style={{ borderRadius: "10px" }}
                      className="sk-video-sec-thumbnail"
                    />
                    <div
                      className="videos-metadataa sk-videosmeta"
                      style={{ position: "relative", top: "15px" }}
                    >
                      <Skeleton
                        count={2}
                        width={280}
                        height={18}
                        style={{ borderRadius: "4px" }}
                        className="sk-video-sec-title"
                      />
                      <div className="views-and-time">
                        <Skeleton
                          count={1}
                          width={170}
                          height={15}
                          style={{ borderRadius: "4px" }}
                          className="sk-video-sec-extra"
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
          <div
            className="sk-uploadedvideos-sectionall2"
            style={
              loading === true && showDiv
                ? { display: "flex" }
                : { display: "none" }
            }
          >
            {myVideos.length > 0 &&
              myVideos.map((element, index) => {
                return (
                  <div
                    className="uploaded-video-contents sk-uploadcontent"
                    key={index}
                    style={{
                      display:
                        element.visibility === "Public" ? "block" : "none",
                    }}
                  >
                    <Skeleton
                      count={1}
                      width={300}
                      height={169}
                      style={{ borderRadius: "10px" }}
                      className="sk-video-sec-thumbnail"
                    />
                    <div
                      className="videos-metadataa sk-videosmeta"
                      style={{ position: "relative", top: "15px" }}
                    >
                      <Skeleton
                        count={2}
                        width={280}
                        height={18}
                        style={{ borderRadius: "4px" }}
                        className="sk-video-sec-title"
                      />
                      <div className="views-and-time">
                        <Skeleton
                          count={1}
                          width={170}
                          height={15}
                          style={{ borderRadius: "4px" }}
                          className="sk-video-sec-extra"
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </SkeletonTheme>
        <div
          className="uploadedvideos-sectionall"
          style={
            loading === true
              ? { visibility: "hidden", display: "none" }
              : { visibility: "visible", display: "grid" }
          }
        >
          {myVideos.length > 0 &&
            myVideos.map((element, index) => {
              return (
                <div
                  className={`${
                    element.visibility === "Private"
                      ? "not-thiss"
                      : "uploaded-video-contents"
                  }`}
                  key={index}
                  style={{
                    display: element.visibility === "Public" ? "block" : "none",
                    position: "relative" // إضافة موضع نسبي للعنصر
                  }}
                  onClick={() => {
                    // إذا كانت القائمة مفتوحة، لا تنتقل إلى صفحة الفيديو
                    if (menuOpen === element._id) {
                      return;
                    }

                    if (user?.email) {
                      updateViews(element._id);
                      setTimeout(() => {
                        navigate(`/video/${element._id}`);
                        window.location.reload();
                      }, 400);
                    } else {
                      navigate(`/video/${element._id}`);
                      window.location.reload();
                    }
                  }}
                >
                  {/* زر الثلاث نقاط - يظهر فقط إذا كان المستخدم هو صاحب الفيديو */}
                  {user?.email === prop?.newmail && (
                    <div
                      className="video-options-button"
                      style={{
                        position: "absolute",
                        top: "5px",
                        right: "5px",
                        zIndex: 10,
                        backgroundColor: theme ? "rgba(0,0,0,0.7)" : "rgba(255,255,255,0.7)",
                        borderRadius: "50%",
                        width: "32px",
                        height: "32px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        cursor: "pointer"
                      }}
                      onClick={(e) => toggleMenu(element._id, e)}
                    >
                      <MoreVertIcon
                        style={{
                          color: theme ? "white" : "black",
                          fontSize: "20px"
                        }}
                      />
                    </div>
                  )}

                  {/* قائمة الخيارات - تظهر فقط عند النقر على زر الثلاث نقاط */}
                  {menuOpen === element._id && (
                    <div
                      className="video-options-menu"
                      style={{
                        position: "absolute",
                        top: "40px",
                        right: "5px",
                        zIndex: 20,
                        backgroundColor: theme ? "#282828" : "white",
                        border: theme ? "1px solid #444" : "1px solid #ddd",
                        borderRadius: "4px",
                        boxShadow: "0 2px 10px rgba(0,0,0,0.2)",
                        padding: "8px 0",
                        minWidth: "120px"
                      }}
                    >
                      <div
                        className="menu-item"
                        style={{
                          padding: "8px 16px",
                          display: "flex",
                          alignItems: "center",
                          color: theme ? "white" : "black",
                          cursor: "pointer",
                          transition: "background-color 0.2s",
                          backgroundColor: "transparent",
                          hover: {
                            backgroundColor: theme ? "#3a3a3a" : "#f5f5f5"
                          }
                        }}
                        onClick={(e) => deleteVideo(element._id, e)}
                      >
                        <DeleteIcon style={{ marginRight: "8px", fontSize: "18px", color: "#f44336" }} />
                        <span>حذف الفيديو</span>
                      </div>
                    </div>
                  )}

                  <img
                    src={element.thumbnailURL}
                    alt="Thumbnail"
                    className="myvidthumbnail"
                    loading="lazy"
                  />
                  <p className="myvideo-duration2 duration-new">
                    {Math.floor(element.videoLength / 60) +
                      ":" +
                      (Math.round(element.videoLength % 60) < 10
                        ? "0" + Math.round(element.videoLength % 60)
                        : Math.round(element.videoLength % 60))}
                  </p>
                  <div
                    className={
                      theme
                        ? "videos-metadataa"
                        : "videos-metadataa text-light-mode"
                    }
                  >
                    <p>
                      {element.Title.length <= 50
                        ? element.Title
                        : `${element.Title.slice(0, 50)}...`}
                    </p>
                    <div
                      className={
                        theme
                          ? "views-and-time"
                          : "views-and-time text-light-mode2"
                      }
                    >
                      <p className="myviews">
                        {element.views >= 1e9
                          ? `${(element.views / 1e9).toFixed(1)}B`
                          : element.views >= 1e6
                          ? `${(element.views / 1e6).toFixed(1)}M`
                          : element.views >= 1e3
                          ? `${(element.views / 1e3).toFixed(1)}K`
                          : element.views}{" "}
                        views
                      </p>
                      <p>&#x2022;</p>
                      <p className="video_published-date">
                        {(() => {
                          const timeDifference =
                            new Date() - new Date(element.uploaded_date);
                          const minutes = Math.floor(timeDifference / 60000);
                          const hours = Math.floor(timeDifference / 3600000);
                          const days = Math.floor(timeDifference / 86400000);
                          const weeks = Math.floor(timeDifference / 604800000);
                          const years = Math.floor(
                            timeDifference / 31536000000
                          );

                          if (minutes < 1) {
                            return "just now";
                          } else if (minutes < 60) {
                            return `${minutes} mins ago`;
                          } else if (hours < 24) {
                            return `${hours} hours ago`;
                          } else if (days < 7) {
                            return `${days} days ago`;
                          } else if (weeks < 52) {
                            return `${weeks} weeks ago`;
                          } else {
                            return `${years} years ago`;
                          }
                        })()}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    </>
  );
}

export default ChannelVideos;
