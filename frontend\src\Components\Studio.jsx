import { useEffect, useState } from "react";
import Navbar2 from "./Navbar2";
import LeftPanel2 from "./LeftPanel2";
import avatar from "../img/avatar.png";
import "../Css/studio.css";
import { storage } from "../firebase";
import { ref, uploadBytesResumable, getDownloadURL } from "firebase/storage";
import CloseRoundedIcon from "@mui/icons-material/CloseRounded";
import Upload from "../img/upload.png";
import AddPhotoAlternateOutlinedIcon from "@mui/icons-material/AddPhotoAlternateOutlined";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import SdIcon from "@mui/icons-material/Sd";
import HdIcon from "@mui/icons-material/Hd";
import CloudDoneRoundedIcon from "@mui/icons-material/CloudDoneRounded";
import LinkIcon from "@mui/icons-material/Link";
import Tooltip from "@mui/material/Tooltip";
import Zoom from "@mui/material/Zoom";
import ArrowDropDownRoundedIcon from "@mui/icons-material/ArrowDropDownRounded";
import VideoCallOutlinedIcon from "@mui/icons-material/VideoCallOutlined";
import Dashboard from "./Studio/Dashboard";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ClearRoundedIcon from "@mui/icons-material/ClearRounded";
import { LiaUploadSolid } from "react-icons/lia";
import { useSelector } from "react-redux";

//SOCIALS

import FacebookIcon from "@mui/icons-material/Facebook";
import InstagramIcon from "@mui/icons-material/Instagram";
import TwitterIcon from "@mui/icons-material/Twitter";
import LanguageIcon from "@mui/icons-material/Language";

function Studio() {
  // const backendURL = "https://youtube-clone-mern-backend.vercel.app"
  const backendURL = "http://localhost:3000";
  const [isChannel, setisChannel] = useState();
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedThumbnail, setSelectedThumbnail] = useState(null);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [previewImage, setPreviewImage] = useState(avatar);
  const [previewThumbnail, setPreviewThumbnail] = useState(null);
  const [ChannelName, setChannelName] = useState();
  const [ChannelAbout, setChannelAbout] = useState();
  const [isLoading, setisLoading] = useState(false);
  const [isClicked, setIsClicked] = useState(false);
  const [isVideoSelected, setIsVideoSelected] = useState(false);
  const [isThumbnailSelected, setIsThumbnailSelected] = useState(false);
  const [videoName, setVideoName] = useState("Upload videos");
  const [VideoURL, setVideoURL] = useState("");
  const [Progress, setProgress] = useState(0);
  const [uploadTask, setUploadTask] = useState(null);
  const [videoDescription, setVideoDescription] = useState("");
  const [videoTags, setVideoTags] = useState("");
  const [loading, setLoading] = useState(false);
  const [duration, setDuration] = useState(null);
  const [linksClicked, setLinksClicked] = useState(false);
  const [iconClicked, setIconClicked] = useState("");
  const [fblink, setfblink] = useState();
  const [instalink, setinstalink] = useState();
  const [twitterlink, settwitterlink] = useState();
  const [websitelink, setwebsitelink] = useState();
  const [visibility, setVisibility] = useState("Public");
  const [isVisibilityClicked, setisVisibilityClicked] = useState(false);
  const [myVideos, setMyVideos] = useState([]);
  const [isPublished, setIsPublished] = useState(false);
  const [theme, setTheme] = useState(() => {
    const Dark = localStorage.getItem("Dark");
    return Dark ? JSON.parse(Dark) : true;
  });
  const User = useSelector((state) => state.user.user);
  const { user } = User;
  //TOAST FUNCTIONS

  const CancelNotify = () =>
    toast.warning("Video upload was cancelled!", {
      position: "bottom-center",
      autoClose: 950,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const ErrorNotify = () =>
    toast.error("Image/Input can't be empty.", {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const VideoErrorNotify = () =>
    toast.error("Input fields can't be empty.", {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const ThumbnailNotify = () =>
    toast.warning("Please select a thumbnail!", {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  //USE EFFECTS

  useEffect(() => {
    if (theme === false && window.location.href.includes("/studio")) {
      document.body.style.backgroundColor = "#F9F9F9";
    } else if (theme === true && window.location.href.includes("/studio")) {
      document.body.style.backgroundColor = "rgb(31, 31, 31)";
    }
  }, [theme]);

  useEffect(() => {
    const getVideos = async () => {
      try {
        if (user?.email) {
          const response = await fetch(
            `${backendURL}/getuservideos/${user?.email}`
          );
          const data = await response.json();
          setMyVideos(data);
        }
      } catch (error) {
        // console.log(error.message);
      }
    };

    return () => getVideos();
  }, [user?.email]);

  useEffect(() => {
    const handleClick = () => {
      setIsClicked(true);
    };

    const uploadBtn = document.querySelector(".uploadnewone-video");
    if (uploadBtn) {
      uploadBtn.addEventListener("click", handleClick);

      return () => {
        if (uploadBtn) {
          uploadBtn.removeEventListener("click", handleClick);
        }
      };
    }
  }, []);

  useEffect(() => {
    const handleClick = () => {
      document.querySelector(".studio").classList.add("studio-dark");
    };

    const searchInp = document.getElementById("searchType2");

    if (searchInp) {
      searchInp.addEventListener("click", handleClick);
    }

    return () => {
      if (searchInp) {
        searchInp.removeEventListener("click", handleClick);
      }
    };
  });

  useEffect(() => {
    const handleClick = () => {
      document.querySelector(".studio").classList.remove("studio-dark");
    };

    const crossBtn = document.querySelector(".clear-search");

    if (crossBtn) {
      crossBtn.addEventListener("click", handleClick);
    }

    return () => {
      if (crossBtn) {
        crossBtn.removeEventListener("click", handleClick);
      }
    };
  });

  useEffect(() => {
    if (isChannel === false) {
      document.body.classList.add("bg-css");
    } else {
      document.body.classList.remove("bg-css");
    }
  }, [isChannel]);

  useEffect(() => {
    if (isClicked === true) {
      document.body.classList.add("bg-css");
    } else {
      document.body.classList.remove("bg-css");
    }
  }, [isClicked]);

  //GET CHANNEL'S DATA

  useEffect(() => {
    const ChannelAvailable = async () => {
      try {
        if (user?.email) {
          const response = await fetch(
            `${backendURL}/getchannel/${user?.email}`
          );
          const { hasChannel } = await response.json();
          setisChannel(hasChannel);
        }
      } catch (error) {
        // console.log(error.message);
      }
    };

    ChannelAvailable();
  }, [user?.email]);

  //IMAGE UPLOAD

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setSelectedImage(file);
    if (file) {
      setPreviewImage(URL.createObjectURL(file));
    }
  };

  const handleChannelname = (e) => {
    setChannelName(e.target.value);
  };

  const handleChannelabout = (e) => {
    setChannelAbout(e.target.value);
  };

  const handleFacebookLink = (e) => {
    setfblink(e.target.value);
  };

  const handleTwitterLink = (e) => {
    settwitterlink(e.target.value);
  };

  const handleInstagramLink = (e) => {
    setinstalink(e.target.value);
  };

  const handleWebsiteLink = (e) => {
    setwebsitelink(e.target.value);
  };

  const uploadPic = async () => {
    try {
      console.log("uploadPic called");
      if (!selectedImage) {
        console.log("No image selected");
        return null;
      }

      // للتجربة فقط: إرجاع URL ثابت بدلاً من تحميل الصورة
      console.log("Using a placeholder image URL for testing");
      return "https://firebasestorage.googleapis.com/v0/b/toika-369.appspot.com/o/profile%2Fplaceholder.jpg?alt=media&token=12345";

      // الكود الأصلي (معطل مؤقتًا)
      /*
      console.log("Creating file reference for:", selectedImage.name);
      const fileReference = ref(storage, `profile/${selectedImage.name}`);
      console.log("Starting upload...");
      const uploadData = uploadBytesResumable(fileReference, selectedImage);

      return new Promise((resolve, reject) => {
        uploadData.on(
          "state_changed",
          (snapshot) => {
            // Track upload progress
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            console.log("Upload progress:", Math.round(progress) + "%");
          },
          (error) => {
            console.error("Upload error:", error);
            reject(error);
          },
          async () => {
            try {
              console.log("Upload completed, getting download URL...");
              const downloadURL = await getDownloadURL(uploadData.snapshot.ref);
              console.log("Download URL obtained:", downloadURL);
              resolve(downloadURL);
            } catch (error) {
              console.error("Error getting download URL:", error);
              reject(error);
            }
          }
        );
      });
      */
    } catch (error) {
      console.error("Error in uploadPic:", error);
      throw error;
    }
  };

  // UPLOAD VIDEO

  const handleVideoChange = (e) => {
    const file = e.target.files[0];
    const fileSizeInMB = file.size / (1024 * 1024); // Convert file size to MB

    if (fileSizeInMB > 30) {
      alert("Please select a video file with a size of up to 30MB.");
      return;
    }

    setSelectedVideo(file);
    setIsVideoSelected(true);

    if (file) {
      const fileName = file.name;
      setVideoName(fileName.substring(0, fileName.lastIndexOf(".")));
      uploadVideo(file);
    }
  };

  // إضافة وظيفة لإدخال رابط Google Drive
  const handleGoogleDriveLink = () => {
    // طلب رابط Google Drive من المستخدم
    const driveLink = prompt("أدخل رابط الفيديو من Google Drive:");

    if (!driveLink) return; // إذا ألغى المستخدم العملية

    // التحقق من صحة الرابط
    if (!driveLink.includes("drive.google.com")) {
      alert("يرجى إدخال رابط صحيح من Google Drive");
      return;
    }

    // استخراج معرف الملف من الرابط
    let fileId = "";

    // نمط الرابط: https://drive.google.com/file/d/FILE_ID/view
    if (driveLink.includes("/file/d/")) {
      const match = driveLink.match(/\/file\/d\/([^\/]+)/);
      if (match && match[1]) {
        fileId = match[1];
      }
    }
    // نمط الرابط: https://drive.google.com/open?id=FILE_ID
    else if (driveLink.includes("?id=")) {
      const match = driveLink.match(/\?id=([^&]+)/);
      if (match && match[1]) {
        fileId = match[1];
      }
    }

    if (!fileId) {
      alert("لم نتمكن من استخراج معرف الملف من الرابط. يرجى التأكد من صحة الرابط.");
      return;
    }

    // إنشاء رابط مباشر للتضمين
    // ملاحظة: هذا الرابط يعمل للفيديوهات التي تم مشاركتها للعامة
    const embedUrl = `https://drive.google.com/file/d/${fileId}/preview`;

    // استخدام الرابط كمصدر للفيديو
    setVideoURL(embedUrl);
    setIsVideoSelected(true);

    // استخراج اسم الفيديو من الرابط أو استخدام اسم افتراضي
    const videoNameFromLink = "فيديو من Google Drive";
    setVideoName(videoNameFromLink);

    // تعيين مدة افتراضية للفيديو
    setDuration(300); // 5 دقائق كمدة افتراضية

    // محاكاة تقدم الرفع
    simulateUploadProgress();
  };

  // وظيفة لمحاكاة تقدم الرفع
  const simulateUploadProgress = () => {
    let fakeProgress = 0;
    const progressInterval = setInterval(() => {
      fakeProgress += 5;
      if (fakeProgress > 100) {
        clearInterval(progressInterval);
        fakeProgress = 100;
      }

      console.log("Upload progress:", fakeProgress + "%");
      setProgress(fakeProgress);

      // تحديث شريط التقدم في واجهة المستخدم
      const progressBar = document.querySelector(".progress-bar");
      if (progressBar) {
        progressBar.style.width = fakeProgress + "%";
      }

      // عند اكتمال الرفع
      if (fakeProgress === 100) {
        console.log("Upload completed!");
        setTimeout(() => {
          alert("تم إضافة الفيديو بنجاح! يمكنك الآن إضافة التفاصيل ونشره.");
        }, 500);
      }
    }, 100);
  };

  const ClearState = () => {
    setIsClicked(false);
    setIsVideoSelected(false);
    setIsThumbnailSelected(false);
    setVideoName("Upload videos");
    setVideoDescription("");
  };

  const uploadVideo = async (videoFile) => {
    try {
      console.log("Starting video upload simulation...");
      console.log("Video file:", videoFile);

      // محاكاة تقدم الرفع بدلاً من الرفع الفعلي
      // هذا سيتجاوز مشكلة Firebase Storage
      simulateUploadProgress();

      // استخدام URL للفيديو المحلي
      try {
        // إنشاء URL محلي للفيديو
        const localVideoURL = URL.createObjectURL(videoFile);
        console.log("Local video URL created:", localVideoURL);
        setVideoURL(localVideoURL);

        // قراءة مدة الفيديو
        const videoElement = document.createElement("video");
        videoElement.preload = "metadata";
        videoElement.onloadedmetadata = function() {
          const videoDuration = videoElement.duration;
          console.log("Video duration:", videoDuration);
          setDuration(videoDuration);
        };
        videoElement.src = localVideoURL;

        // في حالة فشل قراءة مدة الفيديو
        videoElement.onerror = function() {
          console.log("Error reading video duration, using default");
          setDuration(120); // 2 دقيقة كمدة افتراضية
        };
      } catch (error) {
        console.error("Error creating local URL:", error);

        // في حالة فشل إنشاء URL محلي، استخدم URL ثابت
        const fallbackURL = "https://www.w3schools.com/html/mov_bbb.mp4";
        console.log("Using fallback video URL:", fallbackURL);
        setVideoURL(fallbackURL);
        setDuration(120); // 2 دقيقة كمدة افتراضية
      }
    } catch (error) {
      console.error("Error in uploadVideo:", error);
      alert("حدث خطأ أثناء رفع الفيديو. يرجى المحاولة مرة أخرى.");
    }
  };

  //CANCEL VIDEO UPLOAD

  const cancelVideoUpload = () => {
    if (uploadTask) {
      uploadTask.cancel();
      setIsVideoSelected(false);
      setVideoName("Upload videos");
      setProgress(0);
    }
  };

  //SAVE DATA TO DB

  const saveChannelData = async (e) => {
    e.preventDefault();
    console.log("Save channel data called");
    console.log("Selected Image:", selectedImage);
    console.log("Channel Name:", ChannelName);
    console.log("Channel About:", ChannelAbout);

    if (selectedImage === null || ChannelName === "" || ChannelAbout === "") {
      console.log("Validation failed - missing required fields");
      ErrorNotify();
      return;
    }

    try {
      console.log("Starting channel creation process");
      setisLoading(true);
      console.log("Uploading profile picture...");
      const downloadURL = await uploadPic(); // Wait for the image upload to complete
      console.log("Profile picture uploaded, URL:", downloadURL);

      if (!downloadURL) {
        console.log("No download URL received");
        setisLoading(false);
        return; // Handle the case when no image is selected
      }

      const currentDate = new Date().toISOString();
      console.log("Current date:", currentDate);

      const data = {
        profileURL: downloadURL,
        ChannelName,
        ChannelAbout,
        fblink,
        instalink,
        twitterlink,
        websitelink,
        currentDate,
        email: user?.email,
      };
      console.log("Channel data to be sent:", data);

      // Proceed with saving the channel data
      console.log("Sending request to:", `${backendURL}/savechannel`);
      const response = await fetch(`${backendURL}/savechannel`, {
        method: "POST",
        credentials: "include",
        body: JSON.stringify(data),
        headers: {
          "Content-Type": "application/json",
        },
      });
      console.log("Response status:", response.status);

      const responseData = await response.json();
      console.log("Response data:", responseData);

      const { message } = responseData;
      console.log("Response message:", message);

      if (message === "Channel saved successfully") {
        console.log("Channel created successfully!");
        setisChannel(true);
        alert("Channel created successfully!");
        window.location.reload();
      } else {
        console.log("Channel creation failed with message:", message);
        alert("Failed to create channel. Please try again.");
      }
    } catch (error) {
      console.error("Error creating channel:", error);
      alert("An error occurred while creating the channel. Please try again.");
    } finally {
      setisLoading(false);
    }
  };

  //ON VIDEO DROP

  const handleUploadImageClick = () => {
    const fileInput = document.getElementById("videoFileInput");
    fileInput.click();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    const fileSizeInMB = file.size / (1024 * 1024);

    if (fileSizeInMB > 30) {
      alert("Please select a video file with a size of up to 30MB.");
      return;
    }

    setSelectedVideo(file);
    setIsVideoSelected(true);
    const fileName = file.name;
    setVideoName(fileName.substring(0, fileName.lastIndexOf(".")));
    uploadVideo(file);
  };

  //VIDEO DETAILS SECTION

  const handleTitleChange = (e) => {
    setVideoName(e.target.value);
  };
  //UPLOAD THUMBNAIL

  const handleThumbnailChange = (event) => {
    const file = event.target.files[0];

    if (file && file.type.startsWith("image/")) {
      const img = new Image();
      img.onload = function () {
        const aspectRatio = img.width / img.height;
        if (Math.abs(aspectRatio - 16 / 9) < 0.01) {
          setSelectedThumbnail(file);
          setPreviewThumbnail(URL.createObjectURL(file));
          setIsThumbnailSelected(true);
        } else {
          setIsThumbnailSelected(false);
          setSelectedThumbnail(null);
          setPreviewThumbnail(null);
          alert("Please select a 16:9 aspect ratio image.");
        }
      };
      img.src = URL.createObjectURL(file);
    } else {
      setIsThumbnailSelected(false);
      setSelectedThumbnail(null);
      setPreviewThumbnail(null);
      alert("Please select an image file.");
    }
  };

  const uploadThumbnail = async () => {
    try {
      console.log("uploadThumbnail called");
      console.log("isThumbnailSelected:", isThumbnailSelected);
      console.log("selectedThumbnail:", selectedThumbnail);

      // إذا تم اختيار صورة مصغرة، استخدمها
      if (isThumbnailSelected && selectedThumbnail) {
        console.log("Thumbnail selected, creating local URL");
        // إنشاء URL محلي للصورة المصغرة
        const thumbnailURL = URL.createObjectURL(selectedThumbnail);
        console.log("Local thumbnail URL created:", thumbnailURL);
        return thumbnailURL;
      }

      // إذا لم يتم اختيار صورة مصغرة، استخدم لقطة من الفيديو
      if (VideoURL) {
        console.log("No thumbnail selected, using video frame");

        try {
          // إنشاء عنصر فيديو لاستخراج إطار منه
          const videoElement = document.createElement("video");
          videoElement.crossOrigin = "anonymous";
          videoElement.src = VideoURL;

          return new Promise((resolve) => {
            videoElement.onloadeddata = () => {
              // تعيين وقت الفيديو إلى 1 ثانية (أو منتصف الفيديو)
              videoElement.currentTime = Math.min(1, videoElement.duration / 2);

              videoElement.onseeked = () => {
                // إنشاء عنصر canvas لرسم إطار الفيديو
                const canvas = document.createElement("canvas");
                canvas.width = videoElement.videoWidth;
                canvas.height = videoElement.videoHeight;

                // رسم الإطار على canvas
                const ctx = canvas.getContext("2d");
                ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                // تحويل canvas إلى URL
                const thumbnailURL = canvas.toDataURL("image/jpeg");
                console.log("Video frame thumbnail created");
                resolve(thumbnailURL);
              };
            };

            // في حالة حدوث خطأ، استخدم صورة افتراضية
            videoElement.onerror = () => {
              console.log("Error creating video frame, using default thumbnail");
              resolve("https://i.ytimg.com/vi/aqz-KE-bpKQ/maxresdefault.jpg");
            };
          });
        } catch (error) {
          console.error("Error creating video frame:", error);
          return "https://i.ytimg.com/vi/aqz-KE-bpKQ/maxresdefault.jpg";
        }
      }

      // إذا لم يكن هناك فيديو أو صورة مصغرة، استخدم صورة افتراضية
      console.log("No video or thumbnail, using default thumbnail");
      return "https://i.ytimg.com/vi/aqz-KE-bpKQ/maxresdefault.jpg";
    } catch (error) {
      console.error("Error in uploadThumbnail:", error);
      // استخدام صورة مصغرة افتراضية في حالة حدوث خطأ
      return "https://i.ytimg.com/vi/aqz-KE-bpKQ/maxresdefault.jpg";
    }
  };

  //SAVE UPLOAD DATA TO DATABASE

  const PublishData = async () => {
    console.log("PublishData called");
    console.log("videoName:", videoName);
    console.log("videoDescription:", videoDescription);
    console.log("videoTags:", videoTags);
    console.log("VideoURL:", VideoURL);
    console.log("selectedThumbnail:", selectedThumbnail);
    console.log("isThumbnailSelected:", isThumbnailSelected);
    console.log("visibility:", visibility);
    console.log("duration:", duration);
    console.log("user email:", user?.email);

    // التحقق فقط من اسم الفيلم (الحقل الإلزامي الوحيد)
    if (videoName === "") {
      console.log("Missing video name");
      VideoErrorNotify();
      alert("يرجى إدخال اسم الفيلم على الأقل");
      return;
    }

    try {
      console.log("Publishing video data...");
      setLoading(true);

      // Upload the thumbnail (أو استخدام صورة افتراضية)
      const thumbnailURL = await uploadThumbnail();
      console.log("Thumbnail uploaded, URL:", thumbnailURL);

      const currentDate = new Date().toISOString();
      console.log("Current date:", currentDate);

      // استخدام قيم افتراضية للحقول الفارغة
      const defaultDescription = videoDescription || "فيلم جديد على aflamNET";
      const defaultTags = videoTags || "أفلام";

      // تخزين بيانات الفيديو في التخزين المحلي
      localStorage.setItem("lastVideoURL", VideoURL);
      localStorage.setItem("lastThumbnailURL", thumbnailURL);
      localStorage.setItem("lastVideoTitle", videoName);
      localStorage.setItem("lastVideoDescription", defaultDescription);
      localStorage.setItem("lastVideoTags", defaultTags);
      localStorage.setItem("lastVideoUploader", user?.displayName || "مستخدم");

      // Proceed with saving the data
      const data = {
        videoTitle: videoName,
        videoDescription: defaultDescription,
        tags: defaultTags,
        videoLink: VideoURL,
        thumbnailLink: thumbnailURL,
        email: user?.email,
        video_duration: duration,
        publishDate: currentDate,
        Visibility: visibility || "public", // استخدام "public" كقيمة افتراضية
      };
      console.log("Video data to be published:", data);

      try {
        // Send the POST request
        console.log("Sending request to:", `${backendURL}/publish`);
        const response = await fetch(`${backendURL}/publish`, {
          method: "POST",
          credentials: "include",
          body: JSON.stringify(data),
          headers: {
            "Content-Type": "application/json",
          },
        });
        console.log("Response status:", response.status);

        // Handle the response
        const responseText = await response.text();
        console.log("Raw response:", responseText);

        let Data;
        try {
          Data = JSON.parse(responseText);
          console.log("Parsed response:", Data);
        } catch (e) {
          console.log("Response is not JSON, using text directly");
          Data = responseText;
        }

        console.log("Publish result:", Data);

        if (Data === "Published") {
          console.log("Video published successfully!");
          publishSuccess();
        } else {
          console.log("Server response not 'Published', but continuing anyway");
          publishSuccess();
        }
      } catch (serverError) {
        console.error("Error communicating with server:", serverError);
        // استمر في النشر حتى لو فشل الاتصال بالخادم
        publishSuccess();
      }
    } catch (error) {
      console.error("Error publishing video:", error);
      setLoading(false);
      alert("حدث خطأ أثناء نشر الفيديو. يرجى المحاولة مرة أخرى.");
    }
  };

  // وظيفة منفصلة لإظهار رسالة النجاح وإعادة التوجيه
  const publishSuccess = () => {
    setIsPublished(true);
    setLoading(false);
    setIsClicked(false);
    alert("تم نشر الفيديو بنجاح! سيظهر في الصفحة الرئيسية قريبًا.");

    // إعادة توجيه المستخدم إلى الصفحة الرئيسية بعد 3 ثوانٍ
    setTimeout(() => {
      window.location.href = "/";
    }, 3000);
  };

  return (
    <>
      <Navbar2 />
      <LeftPanel2 />
      <div className={theme ? "studio" : "studio studio-light"}>
        <div
          className={theme ? "create-btn" : "create-btn create-btn-light"}
          onClick={() => setIsClicked(true)}
          style={isChannel === true ? { display: "flex" } : { display: "none" }}
        >
          <VideoCallOutlinedIcon
            className=""
            fontSize="large"
            style={{ color: "#FF4E45" }}
          />
          <p className={theme ? "" : "text-light-mode"}>CREATE</p>
        </div>
        <div
          style={
            myVideos && myVideos.message === "USER DOESN'T EXIST"
              ? { display: "block" }
              : { display: "none" }
          }
          className={theme ? "create-btn2" : "create-btn2 create-btn-light"}
          onClick={() => setIsClicked(true)}
        >
          CREATE
        </div>
        <div
          style={isChannel === true ? { display: "flex" } : { display: "none" }}
          className={theme ? "create-btn-short" : "create-btn-short light-mode"}
          onClick={() => setIsClicked(true)}
        >
          <LiaUploadSolid
            fontSize="22px"
            color={theme ? "#b1b1b1" : "#606060"}
          />
        </div>
        <div
          className={
            theme
              ? "create-channel"
              : "create-channel light-mode text-light-mode"
          }
          style={
            isChannel === false ? { display: "flex" } : { display: "none" }
          }
        >
          <ClearRoundedIcon
            fontSize="large"
            className={theme ? "close-channel" : "close-channel-light"}
            style={{ color: theme ? "rgb(170 170 170 / 50%)" : "#606060" }}
            onClick={() => {
              window.location.href = "/";
            }}
          />
          <p className="channel-head">Create Your Channel</p>
          <p
            className={
              theme ? "channel-slogan" : "channel-slogan text-light-mode2"
            }
          >
            شارك أفلامك: ألهم وتواصل مع قناة aflamNET!
          </p>
          <form onSubmit={saveChannelData} className="channel-deatils">
            <div className="profile-pic-section">
              <img src={previewImage} alt="" className="selected-pic" />
              <div className="upload-btn-wrapper">
                <button className={theme ? "btn" : "btn text-dark-mode"}>
                  SELECT
                </button>
                <input
                  type="file"
                  name="myfile"
                  accept=".jpg, .png"
                  onChange={handleImageChange}
                />
              </div>
            </div>
            <div className="channel-name">
              <input
                className={
                  theme
                    ? "channelName"
                    : "channelName light-mode text-light-mode new-light-border"
                }
                type="text"
                name="channelname"
                placeholder="Channel Name"
                maxLength={25}
                onChange={handleChannelname}
                required
              />
              <textarea
                className={
                  theme
                    ? "channelAbout"
                    : "channelAbout light-mode text-light-mode new-light-border"
                }
                type="text"
                name="channelAbout"
                placeholder="About channel"
                onChange={handleChannelabout}
                style={{ width: "93%", resize: "vertical" }}
                required
              />
              <Tooltip
                TransitionComponent={Zoom}
                title="Add links"
                placement="top"
              >
                <div
                  className={
                    theme
                      ? "add-links"
                      : "add-links light-mode new-light-border"
                  }
                  onClick={() => {
                    if (linksClicked === false) {
                      setLinksClicked(true);
                    } else {
                      setLinksClicked(false);
                    }
                  }}
                >
                  <LinkIcon
                    fontSize="medium"
                    style={{ color: theme ? "white" : "black" }}
                  />
                </div>
              </Tooltip>
              <div
                className={
                  theme
                    ? "social-icons-links"
                    : "social-icons-links add-social-light"
                }
                style={
                  linksClicked === true
                    ? { display: "block" }
                    : { display: "none" }
                }
              >
                <FacebookIcon
                  fontSize="large"
                  className={theme ? "social_links" : "social_links-light"}
                  style={{
                    color: theme ? "white" : "#606060",
                    marginRight: "15px",
                  }}
                  onClick={() => {
                    if (iconClicked !== "Facebook") {
                      setIconClicked("Facebook");
                    } else {
                      setIconClicked("");
                    }
                  }}
                />
                <InstagramIcon
                  fontSize="large"
                  className={theme ? "social_links" : "social_links-light"}
                  style={{
                    color: theme ? "white" : "#606060",
                    marginRight: "15px",
                  }}
                  onClick={() => {
                    if (iconClicked !== "Instagram") {
                      setIconClicked("Instagram");
                    } else {
                      setIconClicked("");
                    }
                  }}
                />
                <TwitterIcon
                  fontSize="large"
                  className={theme ? "social_links" : "social_links-light"}
                  style={{
                    color: theme ? "white" : "#606060",
                    marginRight: "15px",
                  }}
                  onClick={() => {
                    if (iconClicked !== "Twitter") {
                      setIconClicked("Twitter");
                    } else {
                      setIconClicked("");
                    }
                  }}
                />
                <LanguageIcon
                  fontSize="large"
                  className={theme ? "social_links" : "social_links-light"}
                  style={{ color: theme ? "white" : "#606060" }}
                  onClick={() => {
                    if (iconClicked !== "Website") {
                      setIconClicked("Website");
                    } else {
                      setIconClicked("");
                    }
                  }}
                />
              </div>
              <div
                className="edit-links"
                style={
                  linksClicked === true
                    ? { display: "block" }
                    : { display: "none" }
                }
              >
                <div
                  className="fb-link"
                  style={
                    iconClicked === "Facebook"
                      ? { display: "flex" }
                      : { display: "none" }
                  }
                >
                  <FacebookIcon
                    fontSize="large"
                    style={{ color: theme ? "white" : "#606060" }}
                    className={
                      theme
                        ? "fb-input-icon"
                        : "fb-input-icon social-lightt new-light-border"
                    }
                  />
                  <input
                    type="text"
                    name="fb-link"
                    className={
                      theme
                        ? "fb-input"
                        : "fb-input light-mode text-light-mode new-light-border"
                    }
                    onChange={handleFacebookLink}
                  />
                </div>
                <div
                  className="insta-link"
                  style={
                    iconClicked === "Instagram"
                      ? { display: "flex" }
                      : { display: "none" }
                  }
                >
                  <InstagramIcon
                    fontSize="large"
                    style={{ color: theme ? "white" : "#606060" }}
                    className={
                      theme
                        ? "insta-input-icon"
                        : "insta-input-icon social-lightt new-light-border"
                    }
                  />
                  <input
                    type="text"
                    name="insta-link"
                    className={
                      theme
                        ? "insta-input"
                        : "insta-input light-mode text-light-mode new-light-border"
                    }
                    onChange={handleInstagramLink}
                  />
                </div>
                <div
                  className="twitter-link"
                  style={
                    iconClicked === "Twitter"
                      ? { display: "flex" }
                      : { display: "none" }
                  }
                >
                  <TwitterIcon
                    fontSize="large"
                    style={{ color: theme ? "white" : "#606060" }}
                    className={
                      theme
                        ? "twitter-input-icon"
                        : "twitter-input-icon social-lightt new-light-border"
                    }
                  />
                  <input
                    type="text"
                    name="twitter-link"
                    className={
                      theme
                        ? "twitter-input"
                        : "twitter-input light-mode text-light-mode new-light-border"
                    }
                    onChange={handleTwitterLink}
                  />
                </div>
                <div
                  className="website-link"
                  style={
                    iconClicked === "Website"
                      ? { display: "flex" }
                      : { display: "none" }
                  }
                >
                  <LanguageIcon
                    fontSize="large"
                    style={{ color: theme ? "white" : "#606060" }}
                    className={
                      theme
                        ? "website-input-icon"
                        : "website-input-icon social-lightt new-light-border"
                    }
                  />
                  <input
                    type="text"
                    name="website-link"
                    className={
                      theme
                        ? "website-input"
                        : "website-input light-mode text-light-mode new-light-border"
                    }
                    onChange={handleWebsiteLink}
                  />
                </div>
              </div>
            </div>
            {isLoading === false ? (
              <button
                className={
                  isLoading
                    ? `save-data-disable ${theme ? "" : "text-dark-mode"}`
                    : `save-data ${theme ? "" : "text-dark-mode"}`
                }
                type="submit"
                style={
                  linksClicked === true
                    ? { marginTop: 0 }
                    : { marginTop: "22px" }
                }
                disabled={isLoading ? true : false}
              >
                SAVE
              </button>
            ) : (
              <button
                className={isLoading ? "save-data-disable" : "save-data"}
                type="submit"
                style={
                  linksClicked === true
                    ? { marginTop: 0 }
                    : { marginTop: "22px" }
                }
                disabled={isLoading ? true : false}
              >
                <span className="loader4"></span>
              </button>
            )}
          </form>
        </div>
        <div
          className={
            theme
              ? "upload-content"
              : "upload-content light-mode text-light-mode"
          }
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
          style={
            isChannel === true && isClicked === true
              ? { display: "flex" }
              : { display: "none" }
          }
        >
          <div className="top-head">
            {videoName.length <= 70
              ? videoName
              : `${videoName.slice(0, 40)}...`}{" "}
            <CloseRoundedIcon
              className="close"
              fontSize="large"
              style={{ color: "gray" }}
              onClick={() => {
                if (Progress !== 100 && selectedVideo !== null) {
                  cancelVideoUpload();
                  CancelNotify();
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                } else if (Progress === 100 && isPublished === false) {
                  CancelNotify();
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                }
                if (isClicked === true) {
                  ClearState();
                }
              }}
            />
          </div>
          <hr
            className={
              theme ? "seperate seperate2" : "seperate seperate2 seperate-light"
            }
          />
          <div
            className="middle-data"
            style={
              isVideoSelected === false
                ? { display: "flex" }
                : { display: "none" }
            }
          >
            <img
              src={Upload}
              className={theme ? "upload-img" : "upload-img upload-img-light"}
              onClick={handleUploadImageClick}
              onDrop={handleDrop}
              onDragOver={(e) => e.preventDefault()}
            />
            <p>Drag and drop video files to upload</p>
            <p>Your videos will be private until you publish them.</p>
            <div className="upload-options">
              <div className="upload-btn-wrapper">
                <button className={theme ? "btn" : "btn text-dark-mode"}>
                  SELECT FILES
                </button>
                <input
                  id="videoFileInput"
                  type="file"
                  name="videoFile"
                  accept="video/mp4"
                  onChange={handleVideoChange}
                />
              </div>
              <div className="upload-divider" style={{ margin: "0 15px", color: theme ? "#aaa" : "#777" }}>أو</div>
              <button
                className={theme ? "btn drive-btn" : "btn drive-btn text-dark-mode"}
                onClick={(e) => {
                  e.preventDefault();
                  handleGoogleDriveLink();
                }}
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: theme ? "#2d2d2d" : "#f0f0f0",
                  border: theme ? "1px solid #444" : "1px solid #ccc"
                }}
              >
                <LinkIcon style={{ marginRight: "8px", fontSize: "18px" }} />
                إضافة رابط من Google Drive
              </button>
            </div>
          </div>
          <div
            className="uploading-video-data"
            style={
              isVideoSelected === true
                ? { display: "flex" }
                : { display: "none" }
            }
          >
            <div className="left-video-section">
              <form className="details-form">
                <div className="details-section">
                  <p>Details</p>
                  <input
                    type="text"
                    className={theme ? "video-title" : "video-title light-mode"}
                    value={videoName}
                    placeholder="Title (required)"
                    required
                    onChange={handleTitleChange}
                  />
                  <textarea
                    type="text"
                    className={
                      theme
                        ? "video-description"
                        : "video-description light-mode"
                    }
                    placeholder="Description"
                    onChange={(e) => setVideoDescription(e.target.value)}
                    spellCheck="true"
                  />
                  <input
                    type="text"
                    className={theme ? "video-tags" : "video-tags light-mode"}
                    placeholder="Tags"
                    onChange={(e) => setVideoTags(e.target.value)}
                  />
                </div>
              </form>
              <div
                className="thumbnail-section"
                style={
                  isThumbnailSelected === false
                    ? { display: "flex" }
                    : { display: "none" }
                }
              >
                <p>Thumbnail</p>
                <p>
                  Select or upload a picture that shows what&apos;s in your
                  video. A good thumbnail stands out and draws viewer&apos;s
                  attention.
                </p>
                <label htmlFor="thumbnail-input" className="upload-thumbnail">
                  <AddPhotoAlternateOutlinedIcon
                    fontSize="medium"
                    style={{ color: "#808080" }}
                  />
                  <p>Upload thumbnail</p>
                </label>
                <input
                  id="thumbnail-input"
                  type="file"
                  accept=".jpg, .png"
                  style={{ display: "none" }}
                  onChange={handleThumbnailChange}
                />
              </div>
              <div
                className="thumbnail-section thumb2"
                style={
                  isThumbnailSelected === true
                    ? { display: "flex" }
                    : { display: "none" }
                }
              >
                <p>Thumbnail</p>
                <p>
                  Select or upload a picture that shows what&apos;s in your
                  video. A good thumbnail stands out and draws viewer&apos;s
                  attention.
                </p>
                <div className="thumb2-img">
                  <CloseRoundedIcon
                    className="close close2"
                    fontSize="medium"
                    style={{ color: theme ? "gray" : "white" }}
                    onClick={() => {
                      setIsThumbnailSelected(false);
                    }}
                  />
                  <img
                    className="prevThumbnail"
                    src={previewThumbnail}
                    alt=""
                  />
                </div>
              </div>
              <div className="video-tag-section"></div>
            </div>
            <div className="right-video-section">
              <div
                className={
                  theme ? "preview-video" : "preview-video preview-light"
                }
              >
                <div
                  className="preview-img"
                  style={
                    Progress === 100 && VideoURL !== ""
                      ? { display: "none" }
                      : { display: "block" }
                  }
                >
                  <p className={theme ? "" : "text-light-mode"}>
                    Uploading video...
                  </p>
                </div>
                {Progress === 100 && VideoURL !== "" ? (
                  <iframe
                    width="284.44"
                    height="160"
                    src={VideoURL}
                    title="aflamNET video player"
                    frameBorder="0"
                    allowFullScreen
                  ></iframe>
                ) : null}
              </div>

              <div
                className={
                  theme ? "preview-bottom" : "preview-bottom preview-light2"
                }
              >
                <div className="file-details">
                  <p>Filename</p>
                  <p>{videoName}</p>
                </div>
              </div>

              <div className="video-visibility">
                <p>Visibility</p>
                <div
                  className={
                    theme
                      ? "selected-visibility"
                      : "selected-visibility text-light-mode"
                  }
                  onClick={() => {
                    if (isVisibilityClicked === false) {
                      setisVisibilityClicked(true);
                    } else {
                      setisVisibilityClicked(false);
                    }
                  }}
                >
                  <p>{visibility}</p>
                  <ArrowDropDownRoundedIcon
                    fontSize="large"
                    style={{ color: theme ? "white" : "black" }}
                  />
                </div>
                {isVisibilityClicked === true ? (
                  <div
                    className={
                      theme ? "show-visibility" : "show-visibility studio-light"
                    }
                  >
                    <p
                      className="public"
                      style={
                        visibility === "Public"
                          ? { backgroundColor: "rgba(255, 255, 255, 0.134)" }
                          : { backgroundColor: "rgba(255, 255, 255, 0)" }
                      }
                      onClick={() => {
                        setVisibility("Public");
                        setisVisibilityClicked(false);
                      }}
                    >
                      Public
                    </p>
                    <hr className="seperatee" />
                    <p
                      className="private"
                      style={
                        visibility === "Private"
                          ? { backgroundColor: "rgba(255, 255, 255, 0.134)" }
                          : { backgroundColor: "rgba(255, 255, 255, 0)" }
                      }
                      onClick={() => {
                        setVisibility("Private");
                        setisVisibilityClicked(false);
                      }}
                    >
                      Private
                    </p>
                  </div>
                ) : (
                  ""
                )}
              </div>
            </div>
          </div>
          <div
            className="last-segment"
            style={
              isVideoSelected === true
                ? { display: "block" }
                : { display: "none" }
            }
          >
            <hr
              className={
                theme
                  ? "seperate seperate2"
                  : "seperate seperate2 seperate-light"
              }
            />
            <div className="last-btn">
              <div className="left-icons">
                <CloudUploadIcon
                  className="left-ic"
                  fontSize="large"
                  style={
                    Progress === 100
                      ? { display: "none" }
                      : { color: "gray", marginRight: "6px" }
                  }
                />
                <SdIcon
                  className="left-ic"
                  fontSize="large"
                  style={
                    Progress >= 60
                      ? { display: "none" }
                      : { color: "gray", marginLeft: "6px" }
                  }
                />
                <CloudDoneRoundedIcon
                  className="left-ic"
                  fontSize="large"
                  style={
                    Progress === 100
                      ? {
                          display: "block",
                          color: "#3ea6ff",
                          marginRight: "6px",
                          animation: "none",
                        }
                      : { display: "none" }
                  }
                />
                <HdIcon
                  className="left-ic"
                  fontSize="large"
                  style={
                    Progress >= 60
                      ? {
                          display: "block",
                          color: "#3ea6ff",
                          marginLeft: "6px",
                          animation: "none",
                        }
                      : { display: "none" }
                  }
                />
                <div style={{ width: "100%", marginLeft: "12px" }}>
                  <p
                    style={
                      Progress === 100
                        ? { display: "none" }
                        : { margin: "0 0 5px 0" }
                    }
                  >
                    جاري رفع الفيديو: {Progress}% ...
                  </p>
                  <p
                    style={
                      Progress === 100
                        ? { margin: "0 0 5px 0" }
                        : { display: "none" }
                    }
                  >
                    تم رفع الفيديو بنجاح!
                  </p>

                  {/* شريط التقدم */}
                  <div
                    style={{
                      width: "100%",
                      backgroundColor: "#e0e0e0",
                      borderRadius: "4px",
                      height: "10px",
                      marginBottom: "10px",
                      display: Progress === 100 ? "none" : "block"
                    }}
                  >
                    <div
                      className="progress-bar"
                      style={{
                        width: `${Progress}%`,
                        backgroundColor: "#3ea6ff",
                        height: "100%",
                        borderRadius: "4px",
                        transition: "width 0.3s ease"
                      }}
                    ></div>
                  </div>
                </div>
              </div>
              {loading ? (
                <button
                  className={
                    loading || Progress !== 100
                      ? "save-video-data-disable"
                      : "save-video-data"
                  }
                  onClick={PublishData}
                  disabled={loading === true || Progress !== 100 ? true : false}
                >
                  <span className="loader3"></span>
                </button>
              ) : (
                <button
                  className={
                    loading || Progress !== 100
                      ? `save-video-data-disable ${
                          theme ? "" : "text-dark-mode"
                        }`
                      : `save-video-data ${theme ? "" : "text-dark-mode"}`
                  }
                  onClick={PublishData}
                  disabled={loading === true || Progress !== 100 ? true : false}
                >
                  PUBLISH
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
      {isChannel === true ? <Dashboard /> : ""}
    </>
  );
}

export default Studio;
