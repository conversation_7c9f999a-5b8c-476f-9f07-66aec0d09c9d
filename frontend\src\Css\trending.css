.main-trending-section {
  position: absolute;
  /* top: 95px; */
  /* left: 450px; */
  width: -webkit-fill-available;
  color: white;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 170px;
}
.main-trending-section2 {
  position: absolute;
  width: 70vw;
  color: white;
  left: 18%;
  top: 14%;
}

.trend-channel-name,
.trend-channel-extras {
  display: flex;
  align-items: center;
}

.trendingIMG {
  border-radius: 100%;
  width: 90px;
}

.trending-top {
  display: flex;
  align-items: center;
}

.trending-top > p {
  font-size: 24px;
  margin-left: 25px;
}

.seperate-three {
  margin-top: 24px;
}
.trending-videos-section {
  position: absolute;
  margin-top: 10px;
}

.trending-thumbnail {
  width: 250px;
  border-radius: 8px;
}

.trending-duration {
  width: fit-content;
  position: relative;
  top: 110px;
  right: 42px;
  background-color: #000000c9;
  padding: 5px;
  border-radius: 4px;
  font-size: 13px;
}
.trending-video-data {
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  margin-bottom: 18px;
}
.trending-oneliner {
  display: flex;
  align-items: center;
  font-size: 13.5px;
  margin-top: 5px;
  color: #858585;
}

.trending-desc {
  font-size: 13.5px;
  color: #858585;
  margin-top: 15px;
}
.trending-video-texts {
  position: relative;
  right: 18px;
}

.t-views,
.t-uploaded-date {
  margin-left: 6px;
}

.trending-batch {
  margin-bottom: 5px;
  font-size: 14.5px;
  color: #3ea6ff;
}

.trending-batch-light {
  margin-bottom: 5px;
  font-size: 14.5px;
  color: #0085e2;
}

/* MEDIA QUERIES */

@media (width<= 1500px) {
  .main-trending-section2 {
    left: 22%;
  }
}

@media (width<= 1215px) {
  .main-trending-section2 {
    left: 26%;
  }
}

@media (width<= 1065px) {
  .main-trending-section2 {
    left: 30%;
    width: 66vw;
  }
  .sk-trend-title {
    width: 35vw !important;
  }
}

@media (width<= 860px) {
  .main-trending-section2 {
    left: 14%;
    width: 82vw;
  }
  .trending-desc {
    display: none;
  }
  .trending-oneliner {
    flex-direction: column;
    align-items: flex-start;
    margin-top: 12px;
  }
  .trend-channel-extras {
    margin-top: 4px;
  }
  .t-views {
    margin-left: 0px;
  }
}

@media (width<= 680px) {
  .main-trending-section2 {
    left: 17%;
    width: 82vw;
  }
}

@media (width<= 650px) {
  .main-trending-section2 {
    left: 4%;
    width: 92vw;
    top: 11%;
  }
}

@media (width<= 600px) {
  .trending-video-data {
    flex-direction: column;
  }
  .trending-thumbnail {
    width: 92vw;
  }
  .trending-video-texts {
    right: 0px;
    bottom: 14px;
    width: 88vw;
  }
  .trending-duration {
    left: 84vw;
    top: -36px !important;
    right: 0px !important;
  }
  .trending-batch,.trending-batch-light {
    font-size: 17.5px;
  }
  .trending-title {
    font-size: 17px;
    margin-top: 10px;
  }
  .trending-oneliner {
    font-size: 15px;
    flex-wrap: wrap;
  }
  .trending-videos-section {
    padding-bottom: 80px;
  }
  .trending-oneliner {
    flex-direction: row;
    align-items: center;
  }
  .trend-channel-extras {
    margin-top: 0px;
  }
  .trend-channel-name {
    margin-right: 6px;
  }

  /* SKELETON CSS  */

  .sk-trend-thumbnail {
    width: 92vw !important;
    height: 50vw !important;
  }
  .sk-trend-title,
  .sk-trend-trending,
  .sk-trend-extra1 {
    left: 0px !important;
  }
  .sk-video-trend {
    margin-top: 12px;
  }
  .sk-trending-data {
    margin-bottom: 35px;
  }
  .sk-trend-title {
    width: 85vw !important;
  }
  .sk-trend-extra1 {
    width: 60vw !important;
  }
}

@media (width<= 460px) {
  .trending-duration {
    left: 82vw;
  }
  .trending-batch,.trending-batch-light {
    font-size: 16px;
  }
  .trending-title {
    font-size: 15.5px;
  }
  .trending-oneliner {
    font-size: 14px;
  }
}

@media (width<= 425px) {
  .trendingIMG {
    width: 65px;
  }
}

@media (width<= 400px) {
  .trending-duration {
    left: 80vw;
  }
  .trending-title {
    font-size: 15px;
  }
}
