require("dotenv").config();
require("./Database/database");
const bcrypt = require("bcrypt");
const userData = require("./Models/user");

async function createUser() {
  try {
    console.log("Attempting to create a test user...");
    
    // Check if the test user already exists
    const existingUser = await userData.findOne({ email: "<EMAIL>" });
    
    if (existingUser) {
      console.log("Test user already exists, deleting...");
      await userData.deleteOne({ email: "<EMAIL>" });
      console.log("Test user deleted successfully");
    }
    
    // Create a new user
    const hashedPassword = await bcrypt.hash("password123", 11);
    const newUser = new userData({
      name: "Test User",
      email: "<EMAIL>",
      password: hashedPassword,
    });
    
    await newUser.save();
    console.log("Test user created successfully");
    
    // List all users
    const allUsers = await userData.find({});
    console.log("All users:", allUsers.map(user => ({ name: user.name, email: user.email })));
    
    process.exit(0);
  } catch (error) {
    console.error("Error creating test user:", error);
    process.exit(1);
  }
}

createUser();
