@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@600&display=swap");

.about-video {
  position: relative;
  right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-video > p {
  font-family: "Open Sans", sans-serif;
  margin-left: 30px;
  font-weight: 700;
  font-size: 15px;
}

.main-section3 {
  display: flex;
  flex-direction: column;
  background-color: #282828;
  position: fixed;
  top: 68px;
  color: white;
  height: 100%;
  width: 270px;
  padding-top: 25px;
  border-right: 1px solid rgba(128, 128, 128, 0.333);
  text-align: center;
  z-index: 2;
}

.main-section3-new {
  display: none;
  flex-direction: column;
  background-color: #282828;
  position: fixed;
  top: 68px;
  color: white;
  height: 100%;
  width: 90px;
  padding-top: 25px;
  border-right: 1px solid rgba(128, 128, 128, 0.333);
  text-align: center;
  z-index: 2;
}

.current-video-thumbnail {
  transition: all 0.2s ease;
  width: 220px;
}

.current-video-duraation {
  font-size: 12px;
  color: white;
  background-color: #000000b9;
  width: fit-content;
  padding-top: 3px;
  padding-bottom: 3px;
  padding-left: 4px;
  padding-right: 4px;
  position: relative;
  border-radius: 3px;
  transition: all 0.2s ease;
  bottom: 32px;
  left: 205px;
}

.ur-vid {
  font-family: "Open Sans", sans-serif;
  font-weight: 700;
  text-align: left;
  font-size: 15px;
}

.current-video-title {
  text-align: left;
  font-size: 12px;
  color: #aaa;
  margin-top: 6px;
}

.thisvideo-mg-data {
  position: relative;
  left: 25px;
}

.thisvideo-mg-data2 {
  margin-top: 25px;
  text-align: left;
  position: relative;
  left: 25px;
}

.watch-video {
  pointer-events: none;

  position: absolute;
  left: 120px;
  top: 120px;
  visibility: hidden;
  transition: all 0.2s ease;
}

.watch-video2 {
  pointer-events: none;

  position: absolute;
  right: 32px;
  top: 83px;
  visibility: hidden;
  transition: all 0.2s ease;
}

.redirect-video {
  cursor: pointer;
  transition: all 0.2s ease;
}

.current-video-thumbnail:hover {
  filter: brightness(0.45);
}

.current-video-thumbnail:hover .current-video-duraation {
  opacity: 0.3;
}

.current-video-thumbnail:hover ~ .watch-video {
  visibility: visible;
}

.current-video-thumbnail:hover ~ .watch-video2 {
  visibility: visible;
}

.first-panel1 {
  padding-top: 13px;
  padding-bottom: 13px;
  position: relative;
  bottom: 15px;
  transition: all 0.15s ease;
}

.first-panel1:hover {
  cursor: pointer;
  background-color: #1f1f1f;
}

/* MEDIA QUERIES */

@media (width<= 860px) {
  .main-section3 {
    display: none !important;
  }
  .main-section3-new {
    display: flex !important;
  }
}
