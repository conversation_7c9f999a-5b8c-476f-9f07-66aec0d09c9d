import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

// Define the initial state
const initialState = {
  user: {},
  status: "idle", // for tracking the status of the fetch
  error: null,
};

// Create an async thunk for fetching user data
export const fetchUserData = createAsyncThunk(
  "user/fetchUserData",
  async () => {
    // URL del backend en producción o desarrollo
    const backendURL = process.env.NODE_ENV === "production"
      ? "https://aflamnet-backend.onrender.com" // Cambia esto por tu URL de backend en producción
      : window.location.hostname === "localhost"
        ? "http://localhost:3000"
        : `http://${window.location.hostname}:3000`;

    console.log("Fetching user data from:", backendURL);

    // أولاً، تحقق من وجود بيانات المستخدم في التخزين المحلي
    const localUserData = localStorage.getItem("userData");
    const isLoggedIn = localStorage.getItem("isLoggedIn");

    if (localUserData && isLoggedIn === "true") {
      try {
        const parsedUserData = JSON.parse(localUserData);
        console.log("Using cached user data from localStorage");

        // تحقق من صحة البيانات المحفوظة محلياً من خلال استدعاء الخادم
        try {
          const response = await fetch(`${backendURL}/userdata`, {
            credentials: "include",
            headers: {
              "Accept": "application/json",
            },
          });

          if (response.ok) {
            const serverData = await response.json();
            if (serverData.success && serverData.user) {
              // تحديث البيانات المحلية بالبيانات الجديدة من الخادم
              localStorage.setItem("userData", JSON.stringify(serverData.user));
              console.log("User data updated from server");
              return serverData;
            }
          } else {
            console.log("Server validation failed, using cached data");
            return { success: true, user: parsedUserData };
          }
        } catch (serverError) {
          console.log("Server unreachable, using cached data:", serverError.message);
          return { success: true, user: parsedUserData };
        }
      } catch (parseError) {
        console.error("Error parsing cached user data:", parseError);
        localStorage.removeItem("userData");
      }
    }

    try {
      const response = await fetch(`${backendURL}/userdata`, {
        credentials: "include",
        headers: {
          "Accept": "application/json",
        },
      });

      if (!response.ok) {
        console.error("Error fetching user data:", response.status, response.statusText);

        // في حالة فشل الاستجابة، امسح البيانات المحلية
        localStorage.removeItem("userData");
        localStorage.removeItem("isLoggedIn");
        localStorage.removeItem("userId");
        localStorage.removeItem("userEmail");

        return { success: false, error: `Error ${response.status}: ${response.statusText}` };
      }

      const data = await response.json();
      console.log("User data received from server:", data);

      // حفظ البيانات في التخزين المحلي عند النجاح
      if (data.success && data.user) {
        localStorage.setItem("userData", JSON.stringify(data.user));
        localStorage.setItem("isLoggedIn", "true");
        if (data.user._id) {
          localStorage.setItem("userId", data.user._id);
        }
        if (data.user.email) {
          localStorage.setItem("userEmail", data.user.email);
        }
      }

      return data;
    } catch (error) {
      console.error("Error fetching user data:", error);

      // في حالة الخطأ، تحقق من وجود بيانات محلية كنسخة احتياطية
      const fallbackUserData = localStorage.getItem("userData");
      if (fallbackUserData && isLoggedIn === "true") {
        try {
          const parsedFallbackData = JSON.parse(fallbackUserData);
          console.log("Using fallback user data from localStorage");
          return { success: true, user: parsedFallbackData };
        } catch (parseError) {
          console.error("Error parsing fallback user data:", parseError);
        }
      }

      return { success: false, error: error.message };
    }
  }
);

// Create a slice
export const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserData.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchUserData.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.user = action.payload;
      })
      .addCase(fetchUserData.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });
  },
});

// Export the reducer
export default userSlice.reducer;
