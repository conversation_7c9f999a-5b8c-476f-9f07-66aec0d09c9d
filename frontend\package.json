{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@coreui/react": "^4.8.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.5", "@reduxjs/toolkit": "^2.2.5", "animate.css": "^4.1.1", "dotenv": "^16.1.4", "firebase": "^9.22.2", "hls.js": "^1.4.6", "plyr": "^3.7.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-icons": "^4.10.1", "react-loading": "^2.0.3", "react-loading-skeleton": "^3.3.1", "react-redux": "^9.1.2", "react-router-dom": "^6.13.0", "react-toastify": "^9.1.3", "react-top-loading-bar": "^2.3.1", "react-useanimations": "^2.10.0"}, "devDependencies": {"@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "vite": "^4.3.9"}}