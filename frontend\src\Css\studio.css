@import url("https://fonts.googleapis.com/css2?family=Geologica:wght@600&display=swap");

.studio {
  background-color: #1f1f1f;
  height: 100vh;
}

.studio-icon {
  padding-left: 30px !important;
}

.create-channel {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #282828;
  color: white;
  padding: 50px;
  display: flex;
  align-items: center;
  flex-direction: column;
  border-radius: 10px;
  z-index: 11;
  animation: fadeUp 0.25s ease-out forwards;
}
.upload-btn-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
  margin-top: 20px;
}

.btn {
  cursor: pointer;
  color: #282828;
  background-color: #3ea6ff;
  padding: 8px 20px;
  border-radius: 3px;
  font-size: 16px;
  border: none;
}

.upload-btn-wrapper input[type="file"] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

.selected-pic {
  width: 120px;
  border-radius: 50%;
  height: 120px;
  object-fit: cover;
}

.channel-head {
  font-size: 35px;
  font-family: "Geologica", sans-serif;
}

.channel-slogan {
  margin-top: 15px;
  font-weight: lighter;
  color: gray;
}
.profile-pic-section {
  margin-top: 35px;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.channel-name {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
}

.channelName,
.channelAbout {
  background-color: #3d3f4359;
  outline: none;
  height: 28px;
  width: 300px;
  padding: 10px;
  color: white;
  margin-bottom: 10px;
  font-size: 16px;
  border: 1px solid rgba(255, 255, 255, 0.27);
  border-radius: 5px;
}

.add-links {
  cursor: pointer;
  background-color: #3d3f4359;
  color: white;
  padding: 8px;
  margin-top: 10px;
  border: 1px solid rgba(255, 255, 255, 0.27);
  width: fit-content;
  border-radius: 5px;
  transition: all 0.1s ease;
}

.add-links:hover {
  background-color: #60636759;
}

.add-links:active {
  transform: scale(0.92);
}

.social-icons-links {
  background-color: #60636759;
  padding: 5px;
  padding-left: 12px;
  padding-right: 12px;
  border-radius: 8px;
  width: fit-content;
  position: relative;
  bottom: 47px;
  left: 54px;
  animation: fadeinanimation 0.2s ease;
}

@keyframes fadeinanimation {
  0% {
    opacity: 0;
    left: 40px;
  }
  100% {
    opacity: 1;
    left: 54px;
  }
}

.social_links,
.social_links-light {
  cursor: pointer;
  transition: all 0.15s ease;
}

.social_links:hover {
  color: rgba(255, 255, 255, 0.699) !important;
}

.channel-deatils {
  margin-top: 0px;
}

.save-data {
  margin-top: 22px;
  cursor: pointer;
  color: #282828;
  background-color: #3ea6ff;
  padding: 8px 20px;
  border-radius: 3px;
  font-size: 16px;
  border: none;
}

.save-data-disable {
  cursor: wait;
  opacity: 45%;
  margin-top: 22px;
  color: #282828;
  background-color: #3ea6ff;
  padding: 8px 20px;
  border-radius: 3px;
  font-size: 16px;
  border: none;
}

.last-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
}

.last-segment {
  position: absolute;
  width: 100%;
  bottom: 0px;
}

.save-video-data {
  cursor: pointer;
  color: #282828;
  background-color: #3ea6ff;
  padding: 8px 20px;
  border-radius: 3px;
  font-size: 16px;
  border: none;
  width: 120px;
  height: 40px;
}

.save-video-data-disable {
  cursor: wait;
  color: #282828;
  background-color: #3ea6ff;
  opacity: 0.5;
  padding: 8px 20px;
  border-radius: 3px;
  font-size: 16px;
  border: none;
  width: 120px;
  height: 40px;
}

.upload-content {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #282828;
  color: white;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  max-width: 100%;
  min-width: 800px;
  height: 750px;
  z-index: 11;
  animation: fadeUp 0.25s ease-out forwards;
}

.seperate2 {
  margin-top: 0;
  margin-bottom: 0;
}

.top-head {
  display: flex;
  align-items: center;
  padding: 25px;
  padding-bottom: 10px;
  padding-right: 12px;
  padding-top: 10px;
  font-size: 18px;
  justify-content: space-between;
}

.upload-img {
  cursor: pointer;
  width: 170px;
}

.middle-data {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  line-height: 34px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.middle-data > p:nth-child(3) {
  font-size: 14px;
  color: gray;
}

.close {
  cursor: pointer;
  transition: all 0.15s ease;
  padding: 5px;
}

.close:hover {
  color: white !important;
  background-color: rgba(128, 128, 128, 0.306);
  border-radius: 50%;
}

.details-section,
.thumbnail-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 40px;
  padding-top: 20px;
}

.thumbnail-section {
  padding-top: 0px !important;
  position: relative;
  bottom: 12px;
}

.details-section > p,
.thumbnail-section > p:nth-child(1) {
  margin-bottom: 20px;
  font-size: 22px;
}

.video-title,
.video-tags {
  background-color: #282828;
  height: 50px;
  width: 380px;
  border: 2px solid rgba(128, 128, 128, 0.337);
  border-radius: 3px;
  padding: 10px;
  color: white;
  font-size: 17px;
}

.video-tags {
  margin-top: 20px;
}

.video-description {
  background-color: #282828;
  height: 70px;
  width: 380px;
  border: 2px solid rgba(128, 128, 128, 0.337);
  border-radius: 3px;
  padding: 10px;
  margin-top: 20px;
  color: white;
  font-size: 17px;
  resize: none;
  padding-top: 30px;
}

.video-title:focus,
.video-description:focus,
.video-tags:focus {
  outline: 2px solid rgb(0, 145, 255);
}
.details-form {
  margin-top: 0;
}

.video-description::placeholder,
.video-title::placeholder,
.video-tags::placeholder {
  position: absolute;
  top: 6px;
  font-size: 14px;
}

.video-title:focus::placeholder,
.video-description:focus::placeholder,
.video-tags:focus::placeholder {
  color: rgb(0, 145, 255);
}

.uploading-video-data {
  display: flex;
}

.right-video-section {
  position: relative;
  top: 64px;
}

.preview-video {
  width: 284.44px;
  height: 160px;
  background-color: #0d0d0d;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.preview-img > p,
.vid-link > p {
  color: #808080;
  font-size: 14px;
}

.left-icons > p {
  color: #9a9a9a;
  font-size: 14px;
}

.video-visibility {
  margin-top: 35px;
}

.file-details > p:nth-child(1) {
  color: #808080;
  font-size: 14px;
}

.vid-link > a {
  color: #207bc5;
  text-decoration: none;
}

.preview-bottom {
  background-color: #1f1f1f;
  padding: 10px;
  line-height: 22px;
  width: 254px;
}
.link-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.preview-bottom {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  padding-left: 15px;
  padding-right: 15px;
}

.copy-btn {
  cursor: pointer;
  padding: 8px;
  transition: all 0.15s ease;
}

.copy-btn:hover {
  color: white !important;
  background-color: rgba(128, 128, 128, 0.306);
  border-radius: 30%;
}

.left-video-section {
  width: 480px;
}

.thumbnail-section > p:nth-child(1) {
  margin-bottom: 15px;
}

.thumbnail-section > p:nth-child(2),
.upload-thumbnail > p {
  color: #808080;
  font-size: 14px;
}

.upload-thumbnail {
  cursor: pointer;
  border: 1.5px solid #808080;
  padding: 15px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  border-radius: 3px;
  border-style: dashed;
  margin-top: 20px;
}

.prevThumbnail {
  cursor: pointer;
  width: 176px;
  height: 100px;
  margin-top: 12px;
  border-radius: 4px;
  object-fit: cover;
  transition: all 0.2s ease;
  border: 2px solid rgba(255, 255, 255, 0.633);
}

.close2 {
  position: absolute;
  left: 184px;
  bottom: 112px;
  padding: 3px;
  z-index: 12;
}

.left-icons {
  display: flex;
  align-items: center;
}

.left-ic {
  animation: colorFade 4s infinite;
}

@keyframes colorFade {
  0% {
    color: gray;
  } /* Start with gray color */
  50% {
    color: #3ea6ff;
  } /* Transition to light blue color */
  100% {
    color: gray;
  } /* Transition back to gray color */
}

.edit-links {
  position: relative;
  bottom: 25px;
}

.fb-link,
.insta-link,
.twitter-link,
.website-link {
  display: flex;
  position: relative;
  align-items: center;
  animation: fadedownanimation 0.2s ease;
}

@keyframes fadedownanimation {
  0% {
    opacity: 0;
    bottom: 30px;
  }

  100% {
    opacity: 1;
    bottom: 0;
  }
}

.fb-input-icon,
.insta-input-icon,
.twitter-input-icon,
.website-input-icon {
  background-color: #60636759;
  padding: 6px;
  padding-left: 10px;
  padding-right: 10px;
  border-top-left-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.27);
  border-right: none;
  border-bottom-left-radius: 5px;
}

.fb-input,
.insta-input,
.twitter-input,
.website-input {
  background-color: #3d3f4359;
  outline: none;
  height: 27px;
  width: 100%;
  padding: 10px;
  color: white;
  font-size: 16px;
  border: 1px solid rgba(255, 255, 255, 0.27);
  border-left: none;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

.selected-visibility {
  height: 50px;
  background-color: rgba(255, 255, 255, 0);
  color: white;
  cursor: pointer;
  outline: none;
  height: 50px;
  margin-top: 10px;
  border: 2px solid rgba(128, 128, 128, 0.337);
  border-radius: 5px;
  font-size: 15px;
  width: -webkit-fill-available;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 15px;
  padding-right: 15px;
}

.seperatee {
  border: 1px solid #5b5d6265 !important;
}

.show-visibility {
  position: relative;
  background-color: rgba(255, 255, 255, 0);
  color: white;
  cursor: pointer;
  outline: none;
  margin-top: 10px;
  border: 2px solid rgba(128, 128, 128, 0.337);
  border-radius: 5px;
  font-size: 15px;
  width: -webkit-fill-available;
  animation: pulldown 0.16s ease;
}

@keyframes pulldown {
  0% {
    bottom: 20px;
    opacity: 0;
  }
  100% {
    bottom: 0;
    opacity: 1;
  }
}
.public,
.private {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
}

.create-btn2 {
  display: flex;
  opacity: 0;
  cursor: pointer;
  align-items: center;
  border: 1px solid black;
  padding-left: 10px;
  padding-right: 10px;
  transition: all 0.2s ease;
  position: absolute;
  top: 489px;
  left: 445px;
  padding: 9px;
  padding-left: 28px;
  padding-right: 56px;
  z-index: 2;
}

.close-channel,.close-channel-light {
  cursor: pointer;
  position: absolute;
  right: 12px;
  top: 12px;
  padding: 5px;
  border-radius: 100%;
}

.close-channel:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.163);
}

.loader4 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-block;
  border-top: 3px solid #fff;
  border-right: 3px solid transparent;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* MEDIA QUERIES */

@media (width<= 860px) {
  .upload-content {
    width: 90vw !important;
    max-width: none !important;
    min-width: auto !important;
  }
  .uploading-video-data {
    flex-direction: column;
    overflow: scroll;
    height: 80%;
  }
  .left-video-section {
    width: -webkit-fill-available;
  }
  .details-form {
    width: inherit;
  }
  .details-section {
    width: inherit;
  }
  .video-title,
  .video-description,
  .video-tags {
    width: inherit;
  }
  .right-video-section {
    top: 0px;
    padding-left: 40px;
    padding-bottom: 40px;
    padding-right: 40px;
  }
  .selected-visibility {
    width: max-content;
  }
  .create-btn2 {
    left: 259px;
  }

  /* CREATE CHANNEL  */

  .channel-head {
    font-size: 28px;
  }

  .channel-slogan {
    font-size: 15px;
    text-align: center;
  }
}

@media (width<= 720px) {
  .upload-img {
    width: 135px;
  }
  .middle-data {
    width: max-content;
  }
}

@media (width<= 650px) {
  .create-btn-short {
    right: 18px;
  }
  .upload-content {
    height: 78%;
  }
  .uploading-video-data {
    height: 77%;
  }
  .details-section > p,
  .thumbnail-section > p:nth-child(1) {
    font-size: 18px;
  }
  .save-video-data-disable {
    font-size: 14.5px;
    width: 105px;
  }
  .create-btn2 {
    left: 173px;
    top: 497px;
  }
}

@media (width<= 520px) {
  .middle-data > p:nth-child(2) {
    font-size: 15.4px;
  }
  .middle-data > p:nth-child(3) {
    font-size: 13px;
  }
  .upload-btn-wrapper > button {
    font-size: 15px;
  }
}

@media (width<= 460px) {
  .upload-content {
    height: 100%;
    width: 100% !important;
    border-radius: 0%;
  }

  .uploading-video-data {
    height: 82%;
  }

  .details-section,
  .thumbnail-section,
  .right-video-section {
    padding-left: 25px;
    padding-right: 25px;
  }

  .upload-img {
    width: 110px;
  }
  .upload-btn-wrapper > button {
    font-size: 13.5px;
  }
  .middle-data > p:nth-child(2) {
    font-size: 14.4px;
  }
  .middle-data > p:nth-child(3) {
    font-size: 12px;
  }
  .left-ic {
    font-size: 1.9rem !important;
  }
  .close2 {
    left: 172px;
  }
  .create-btn2 {
    left: 33%;
    top: 535px;
  }

  /* CREATE CHANNEL  */

  .create-channel {
    width: 72vw !important;
  }
  .channel-head {
    font-size: 24px;
    text-align: center;
  }
  .channel-slogan {
    font-size: 14px;
  }
  .selected-pic {
    width: 90px;
    height: 90px;
  }
  .channelName,
  .channelAbout {
    height: 18px;
    font-size: 14.6px;
    width: 78vw !important;
  }
  .fb-input,
  .insta-input,
  .twitter-input,
  .website-input {
    font-size: 15px;
  }
}
