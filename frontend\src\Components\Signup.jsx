import { useState } from "react";
import "../Css/navbar.css";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

function Signup() {
  // URL del backend en producción o desarrollo
  const backendURL = process.env.NODE_ENV === "production"
    ? "https://aflamnet-backend.onrender.com" // Cambia esto por tu URL de backend en producción
    : window.location.hostname === "localhost"
      ? "http://localhost:3000"
      : `http://${window.location.hostname}:3000`;
  const [data, setData] = useState({});
  const [theme, setTheme] = useState(() => {
    const Dark = localStorage.getItem("Dark");
    return Dark ? JSON.parse(Dark) : true;
  });

  //TOASTS

  const SignupNotify = () =>
    toast.success("Signup successfull!", {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const ErrorNotify = () =>
    toast.error("Input fields can't be empty.", {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const EmailErrorNotify = (data) =>
    toast.error(data, {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const handleInputs = (e) => {
    setData({
      ...data,
      [e.target.name]: e.target.value,
    });
  };

  const SubmitData = async (e) => {
    e.preventDefault();
    if (!data.name || !data.email || !data.password) {
      ErrorNotify();
      return;
    }
    try {
      // عرض رسالة التحميل
      toast.info("جاري إنشاء الحساب...", {
        position: "top-center",
        autoClose: 1000,
      });

      console.log("Submitting signup data:", data);
      console.log("Backend URL:", backendURL);

      // محاولة إنشاء الحساب مع إعادة المحاولة
      let retryCount = 0;
      let success = false;
      let responseData;

      while (retryCount < 3 && !success) {
        try {
          const response = await fetch(`${backendURL}/signup`, {
            method: "POST",
            credentials: "include",
            body: JSON.stringify(data),
            headers: {
              "Content-Type": "application/json",
              "Accept": "application/json",
            },
            mode: "cors"
          });

          console.log(`Attempt ${retryCount + 1} - Response status:`, response.status);

          if (response.ok) {
            responseData = await response.json();
            console.log("Response data:", responseData);
            success = true;
          } else {
            // إذا كان هناك خطأ في الاستجابة، انتظر قليلاً قبل إعادة المحاولة
            responseData = await response.json();
            console.log(`Attempt ${retryCount + 1} failed:`, responseData);
            retryCount++;

            if (retryCount < 3) {
              await new Promise(resolve => setTimeout(resolve, 1000)); // انتظر ثانية واحدة
            }
          }
        } catch (fetchError) {
          console.error(`Attempt ${retryCount + 1} error:`, fetchError);
          retryCount++;

          if (retryCount < 3) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // انتظر ثانية واحدة
          }
        }
      }

      if (!success) {
        throw new Error("فشلت جميع محاولات إنشاء الحساب");
      }

      const { message, user } = responseData;

      if (message === "REGISTRATION SUCCESSFUL") {
        SignupNotify();

        // حفظ معلومات تسجيل الدخول في التخزين المحلي كنسخة احتياطية
        localStorage.setItem("isLoggedIn", "true");
        localStorage.setItem("loginTime", new Date().toISOString());
        localStorage.setItem("userData", JSON.stringify(user || {}));

        // محاولة جلب بيانات المستخدم مباشرة بعد إنشاء الحساب
        try {
          const userResponse = await fetch(`${backendURL}/userdata`, {
            credentials: "include",
            headers: {
              "Accept": "application/json",
            },
          });

          if (userResponse.ok) {
            const userData = await userResponse.json();
            console.log("User data fetched after signup:", userData);

            if (userData.success && userData.user) {
              localStorage.setItem("userData", JSON.stringify(userData.user));
            }
          }
        } catch (userDataError) {
          console.error("Error fetching user data after signup:", userDataError);
        }

        setTimeout(() => {
          window.location.reload();
          document.body.classList.remove("bg-class");
        }, 2000);
      } else {
        EmailErrorNotify(message);
      }
    } catch (error) {
      console.error("Signup error:", error);
      toast.error(`خطأ في إنشاء الحساب: ${error.message}`, {
        position: "top-center",
        autoClose: 3000,
      });
    }
  };

  return (
    <>
      <div className="above-data">
        <p className="signup-head">Create Your Account</p>
        <p className="signup-desc">
          Unlock Your World of Entertainment, Unlock Your World of
          Entertainment, Join the YouTube Community
        </p>
      </div>
      <div className="signup-form">
        <form onSubmit={SubmitData}>
          <input
            type="text"
            name="name"
            className={
              theme
                ? "username"
                : "username email-light light-mode text-light-mode"
            }
            placeholder="Name"
            required
            onChange={handleInputs}
          />
          <input
            type="email"
            name="email"
            className={
              theme ? "email" : "email email-light light-mode text-light-mode"
            }
            placeholder="Email Address"
            required
            onChange={handleInputs}
          />
          <input
            type="password"
            name="password"
            className={
              theme
                ? "password"
                : "password email-light light-mode text-light-mode"
            }
            placeholder="Passcode"
            required
            onChange={handleInputs}
          />
          <button
            className={theme ? "signup-btn" : "signup-btn signin-btn-light"}
            type="submit"
          >
            Create Your Account
          </button>
        </form>
      </div>
    </>
  );
}

export default Signup;
