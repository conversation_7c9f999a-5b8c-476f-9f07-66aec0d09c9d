import { useEffect, useState } from "react";
import "../Css/navbar.css";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Reset from "./Reset";

function Signin(prop) {
  // URL del backend en producción o desarrollo
  const backendURL = process.env.NODE_ENV === "production"
    ? "https://aflamnet-backend.onrender.com" // Cambia esto por tu URL de backend en producción
    : window.location.hostname === "localhost"
      ? "http://localhost:3000"
      : `http://${window.location.hostname}:3000`;
  const [data, setData] = useState({});
  const [showReset, setShowReset] = useState(false);
  const [theme, setTheme] = useState(() => {
    const Dark = localStorage.getItem("Dark");
    return Dark ? JSON.parse(Dark) : true;
  });

  useEffect(() => {
    if (prop.close === true) {
      setShowReset(false);
    }
  }, [prop.close]);

  useEffect(() => {
    if (prop.switch === false) {
      setShowReset(false);
    }
  }, [prop.switch]);

  //TOASTS

  const LoginNotify = () =>
    toast.success("Login successfull!", {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const InvalidNotify = () =>
    toast.error("Invalid Credentials!", {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const ErrorNotify = () =>
    toast.error("Input fields can't be empty.", {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const NoUserNotify = () =>
    toast.error("User doesn't exists.", {
      position: "top-center",
      autoClose: 1200,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: theme ? "dark" : "light",
    });

  const handleInputs = (e) => {
    setData({
      ...data,
      [e.target.name]: e.target.value,
    });
  };

  const SubmitData = async (e) => {
    e.preventDefault();
    if (!data.email1 || !data.password1) {
      ErrorNotify();
      return;
    }
    try {
      console.log("Submitting login data:", data);
      console.log("Backend URL:", backendURL);

      // عرض رسالة التحميل
      toast.info("جاري تسجيل الدخول...", {
        position: "top-center",
        autoClose: 1000,
      });

      // محاولة تسجيل الدخول مع إعادة المحاولة
      let retryCount = 0;
      let success = false;
      let responseData;

      while (retryCount < 3 && !success) {
        try {
          const response = await fetch(`${backendURL}/login`, {
            method: "POST",
            body: JSON.stringify(data),
            credentials: "include",
            headers: {
              "Content-Type": "application/json",
              "Accept": "application/json",
            },
            mode: "cors"
          });

          console.log(`Attempt ${retryCount + 1} - Response status:`, response.status);

          if (response.ok) {
            responseData = await response.json();
            console.log("Response data:", responseData);
            success = true;
          } else {
            // إذا كان هناك خطأ في الاستجابة، انتظر قليلاً قبل إعادة المحاولة
            responseData = await response.json();
            console.log(`Attempt ${retryCount + 1} failed:`, responseData);
            retryCount++;

            if (retryCount < 3) {
              await new Promise(resolve => setTimeout(resolve, 1000)); // انتظر ثانية واحدة
            }
          }
        } catch (fetchError) {
          console.error(`Attempt ${retryCount + 1} error:`, fetchError);
          retryCount++;

          if (retryCount < 3) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // انتظر ثانية واحدة
          }
        }
      }

      if (!success) {
        throw new Error("فشلت جميع محاولات تسجيل الدخول");
      }

      const { message } = responseData;

      if (message === "LOGIN SUCCESSFUL") {
        console.log("Login successful!");
        LoginNotify();

        // حفظ معلومات تسجيل الدخول في التخزين المحلي كنسخة احتياطية
        localStorage.setItem("isLoggedIn", "true");
        localStorage.setItem("loginTime", new Date().toISOString());

        // محاولة جلب بيانات المستخدم مباشرة بعد تسجيل الدخول
        try {
          const userResponse = await fetch(`${backendURL}/userdata`, {
            credentials: "include",
            headers: {
              "Accept": "application/json",
            },
          });

          if (userResponse.ok) {
            const userData = await userResponse.json();
            console.log("User data fetched after login:", userData);

            if (userData.success && userData.user) {
              localStorage.setItem("userData", JSON.stringify(userData.user));
            }
          }
        } catch (userDataError) {
          console.error("Error fetching user data after login:", userDataError);
        }

        setTimeout(() => {
          window.location.reload();
          document.body.classList.remove("bg-class");
        }, 2000);
      } else if (message === "INVALID CREDENTIALS") {
        console.log("Invalid credentials!");
        InvalidNotify();
      } else if (message === "USER DOESN'T EXIST") {
        console.log("User doesn't exist!");
        NoUserNotify();
      } else {
        console.log("Unknown response message:", message);
        toast.error("حدث خطأ غير معروف. يرجى المحاولة مرة أخرى.", {
          position: "top-center",
          autoClose: 3000,
        });
      }
    } catch (error) {
      console.error("Login error:", error);
      toast.error(`خطأ في تسجيل الدخول: ${error.message}`, {
        position: "top-center",
        autoClose: 3000,
      });
    }
  };

  return (
    <>
      <div
        className="above-data"
        style={{ display: showReset ? "none" : "block" }}
      >
        <p className="signup-head">Login to Your Account</p>
        <p className="signup-desc">
          Stay Connected-Stay Entertained, Step into the World of YouTube, Join
          the YouTube Community
        </p>
      </div>
      <div
        className="signup-form"
        style={{ display: showReset ? "none" : "flex" }}
      >
        <form onSubmit={SubmitData}>
          <input
            type="email"
            name="email1"
            className={
              theme ? "email" : "email email-light light-mode text-light-mode"
            }
            placeholder="Email Address"
            onChange={handleInputs}
            required
          />
          <input
            type="password"
            name="password1"
            className={
              theme
                ? "password"
                : "password email-light light-mode text-light-mode"
            }
            placeholder="Passcode"
            onChange={handleInputs}
            required
          />
          <p
            className={
              theme ? "forgot-password" : "forgot-password text-light-mode"
            }
            onClick={() => setShowReset(true)}
          >
            Forgot password?
          </p>
          <button
            className={theme ? "signup-btn" : "signup-btn signin-btn-light"}
            type="submit"
          >
            Login to Your Account
          </button>
        </form>
      </div>
      <div
        className="password-reset"
        style={{ display: showReset ? "block" : "none" }}
      >
        <Reset />
      </div>
    </>
  );
}

export default Signin;
