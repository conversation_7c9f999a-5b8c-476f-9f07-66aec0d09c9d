.popular-categories {
  color: white;
  display: flex;
  align-items: center;
  padding: 15px;
  height: fit-content;
  width: -webkit-fill-available;
  position: fixed;
  z-index: 4;
  background-color: #0f0f0f;
  top: 67px;
  height: 40px;
  overflow: scroll;
}

.top-tags {
  cursor: pointer;
  background-color: #292929;
  padding: 12px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 400;
  margin-left: 25px;
  transition: all 0.18s ease;
}

.top-tags > p {
  width: max-content;
}

/* 
.top-tags:hover {
  background-color: #3f3f3f;
} */

.tag-color {
  background-color: white;
  color: #0f0f0f;
}

.tag-color-light {
  background-color: black;
  color: white;
}

.tagcolor-newlight {
  background-color: #f0f0f0;
  color: black;
}

.browse-data {
  background-color: #0f0f0f;
  width: calc(100% - 250px);
  position: relative;
  left: 250px;
  top: 100px;
}

.browse-data2 {
  background-color: #0f0f0f;
  width: 95%;
  position: relative;
  left: 250px;
  top: 100px;
}

.browse {
  background-color: #0f0f0f;
  display: flex;
}

.uploaded-videos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(315px, 1fr));
  /* grid-template-columns: repeat(4, 1fr); */
  /* grid-template-rows: repeat(4, 1fr); */
  justify-content: center;
  grid-column-gap: 19px;
  grid-row-gap: 12px;
}

.uploaded-videos2 {
  display: grid;
  /* grid-template-columns: repeat(auto-fit, minmax(315px, 1fr)); */
  grid-template-columns: repeat(4, 1fr);
  /* grid-template-rows: repeat(4, 1fr); */
  grid-column-gap: 8px;
  grid-row-gap: 12px;
}

@keyframes FADEINN {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.video-section {
  color: white;
  margin: 60px;
}

.video-data {
  cursor: pointer;
  /* width: 360px; */
  width: fit-content;
}

.title {
  font-weight: 500 !important;
}

.title2 {
  font-weight: 500 !important;
}

.uploader {
  font-size: 14px;
  color: rgb(130, 130, 130);
  transition: all 0.12s ease;
}

.uploader:hover {
  color: white;
}

.uploader2 {
  font-size: 14px;
  color: rgb(130, 130, 130);
  transition: all 0.12s ease;
}

.uploader2:hover {
  color: white;
}

.menu,
.menu2,
.menu-light {
  cursor: pointer;
  transition: all 0.15s ease;
  padding: 7px;
  border-radius: 50%;
}

.menu:hover,
.menu2:hover {
  color: white !important;
  background-color: #3d3f4365;
}

.menu-light:hover {
  background-color: #f0f0f0 !important;
}

.channel-basic-data {
  display: flex;
  position: relative;
  bottom: 25px;
}

.channel-basic-data2 {
  display: flex;
  position: relative;
  bottom: 25px;
}

.channel-profile {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 100%;
  margin-top: 8px;
}
.channel-profile2 {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 100%;
  margin-top: 8px;
}

.channel-text-data {
  margin-left: 15px;
  width: 250px;
}

.channel-text-data2 {
  margin-left: 15px;
  width: 250px;
}

.duration {
  width: fit-content;
  position: relative;
  bottom: 37px;
  left: 285px;
  background-color: #000000c9;
  padding: 5px;
  border-radius: 4px;
  font-size: 13px;
}

.durationn2 {
  width: fit-content;
  position: relative;
  bottom: 37px;
  background-color: #000000c9;
  padding: 5px;
  border-radius: 4px;
  font-size: 13px;
}

.spinner {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: relative;
  color: white;
  top: 150px;
}

.view-time {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.view-time > p {
  font-size: 14px;
  color: rgb(130, 130, 130);
}

.view-time23 {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.view-time23 > p {
  font-size: 14px;
  color: rgb(130, 130, 130);
}

.video-uploader {
  display: flex;
  align-items: center;
}

.video-uploader2 {
  display: flex;
  align-items: center;
}

.durationn2 {
  left: 255px;
}

/* MEDIA QUERIES */

@media (max-width: 1100px) {
  .browse-thumbnails {
    width: 270px !important;
  }

  .duration {
    bottom: 32px;
    left: 232px;
    font-size: 11px;
  }

  .channel-basic-data {
    width: 83%;
  }
  .title {
    font-size: 15px;
  }
  .channel-profile {
    width: 34px;
    height: 34px;
  }
  .uploaded-videos {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    padding-right: 0px !important;
  }
  .video-section {
    margin-right: 0px;
  }
}

@media (width < 920px) {
  .sk-browse-vid {
    width: 270px !important;
    height: 152px !important;
  }
  .sk-browse-title {
    width: 200px !important;
  }
}

@media (max-width: 860px) {
  .browse-data,
  .browse-data2 {
    left: 74px !important;
  }

  .browse-thumbnails {
    width: 290px !important;
  }

  .uploaded-videos,
  .uploaded-videos2 {
    grid-template-columns: repeat(auto-fit, minmax(290px, 1fr));
    width: 82vw;
  }

  .duration {
    left: 249px;
    font-size: 12px;
  }
  .channel-basic-data {
    width: 92%;
  }
  .title {
    font-size: 16px;
  }
  .sk-browse-vid {
    width: 290px !important;
    height: 163px !important;
  }
  .sk-browse-title {
    width: 200px !important;
  }
}

@media (max-width: 740px) {
  .uploaded-videos,
  .uploaded-videos2 {
    width: 66vw !important;
    position: relative;
    left: 60px;
  }
  .browse-thumbnails {
    width: 450px !important;
  }
  .duration {
    left: 403px;
    bottom: 38px;
    font-size: 14px;
  }
  .channel-profile {
    width: 45px;
    height: 45px;
  }
  .channel-text-data {
    width: 365px;
  }
  .title {
    font-size: 17px;
  }
  .video-uploader > p,
  .view-time > p {
    font-size: 15.6px;
  }
}

@media (max-width: 650px) {
  .browse-data,
  .browse-data2 {
    left: 0 !important;
  }
  .popular-categories {
    top: 55px !important;
  }
}

@media (max-width: 580px) {
  .video-section {
    margin-left: 6px !important;
    margin-top: 40px !important;
  }
  .uploaded-videos,
  .uploaded-videos2 {
    left: 50px;
  }
  .video-data {
    padding-right: 20px;
  }
  .sk-browse-vid {
    width: 450px !important;
    height: 253px !important;
  }
  .sk-browse-title {
    width: 350px !important;
    height: 20px !important;
  }
  .sk-browse-profile {
    width: 48px !important;
    height: 48px !important;
  }
}

@media (max-width: 520px) {
  .uploaded-videos,
  .uploaded-videos2 {
    left: 16%;
  }

  .browse-thumbnails {
    width: 370px !important;
    margin-right: 20px;
  }

  .duration {
    left: 324px;
    bottom: 35px;
    font-size: 13px;
  }
  .video-data {
    width: inherit;
  }
  .channel-basic-data {
    width: 70vw;
  }
  .channel-profile {
    width: 38px;
    height: 38px;
  }
  .title {
    font-size: 15.5px;
  }
  .video-uploader > p,
  .view-time > p {
    font-size: 14.5px;
  }
}

@media (max-width: 475px) {
  .uploaded-videos,
  .uploaded-videos2 {
    left: 20%;
  }
}

@media (max-width: 425px) {
  .browse-thumbnails {
    width: 325px !important;
    margin-right: 10px;
  }
  .duration {
    left: 283px;
    bottom: 35px;
    font-size: 12px;
  }
  .channel-basic-data {
    width: 315px;
  }
  .uploaded-videos {
    left: 40px;
  }
  .uploaded-videos2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    left: 28px;
  }
  .sk-browse-vid {
    width: 325px !important;
    height: 183px !important;
  }
  .sk-browse-title {
    width: 220px !important;
    height: 15px !important;
  }
  .sk-browse-profile {
    width: 40px !important;
    height: 40px !important;
  }
}

@media (max-width: 380px) {
  .uploaded-videos2 {
    left: 12px;
  }
}

@media (width<= 355px) {
  .video-section {
    margin-left: 4px !important;
  }
}
