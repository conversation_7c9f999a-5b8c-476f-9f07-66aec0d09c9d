require("dotenv").config();
const jwt = require("jsonwebtoken");

// استخدام قيمة ثابتة للمفتاح السري في حالة عدم وجود متغير البيئة
const SECRET_KEY = process.env.SECRET_KEY || "toika_369_secret_key_for_jwt_tokens";

const generateAccessToken = (user) => {
  const accessToken = jwt.sign({ id: user._id }, SECRET_KEY, {
    expiresIn: "1d",
  });
  return accessToken;
};

const generateRefreshToken = (user) => {
  const refreshToken = jwt.sign({ id: user._id }, SECRET_KEY, {
    expiresIn: "7d",
  });
  return refreshToken;
};

const verifyAccessToken = (token) => {
  return jwt.verify(token, SECRET_KEY);
};

const verifyRefreshToken = (token) => {
  return jwt.verify(token, SECRET_KEY);
};

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
  verifyRefreshToken,
};
