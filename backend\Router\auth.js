require("dotenv").config();
require("../Database/database");
const express = require("express");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const cookieParser = require("cookie-parser");
const userData = require("../Models/user");
const auth = express.Router();
const nodemailer = require("nodemailer");
const {
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
} = require("../lib/tokens");

auth.use(cookieParser());

auth.post("/signup", async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // تعطيل التحقق من وجود ملفات تعريف الارتباط مؤقتًا
    // const availableAccessToken = req.cookies?.accessToken;
    // const availableRefreshToken = req.cookies?.refreshToken;

    // if (availableAccessToken || availableRefreshToken) {
    //   return res.status(400).json({
    //     message: "You are already logged in",
    //   });
    // }

    // مسح ملفات تعريف الارتباط الحالية إذا وجدت
    if (req.cookies?.accessToken) {
      // Limpiar con configuración None
      res.clearCookie("accessToken", {
        httpOnly: false,
        sameSite: "None",
        secure: false,
        path: "/"
      });
      // Limpiar también con configuración Lax
      res.clearCookie("accessToken", {
        httpOnly: false,
        sameSite: "Lax",
        secure: false,
        path: "/"
      });
    }
    if (req.cookies?.refreshToken) {
      // Limpiar con configuración None
      res.clearCookie("refreshToken", {
        httpOnly: false,
        sameSite: "None",
        secure: false,
        path: "/"
      });
      // Limpiar también con configuración Lax
      res.clearCookie("refreshToken", {
        httpOnly: false,
        sameSite: "Lax",
        secure: false,
        path: "/"
      });
    }

    console.log("Checking if user exists with email:", email);
    const user = await userData.findOne({ email });
    console.log("User found:", user);

    if (user) {
      return res.status(400).json({
        success: false,
        message: "USER ALREADY EXISTS",
      });
    }
    const hashedPassword = await bcrypt.hash(password, 11);
    const saveData = new userData({
      name,
      email,
      password: hashedPassword,
    });
    await saveData.save();

    //Create access token
    const accessToken = generateAccessToken(saveData);
    const refreshToken = generateRefreshToken(saveData);

    // تعطيل إرسال البريد الإلكتروني مؤقتًا
    console.log("Email sending disabled temporarily");

    res
      .cookie("accessToken", accessToken, {
        httpOnly: false,
        sameSite: process.env.NODE_ENV === "production" ? "None" : "Lax",
        secure: process.env.NODE_ENV === "production",
        maxAge: 24 * 60 * 60 * 1000, // 24 ساعة
        path: "/",
        domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
      })
      .cookie("refreshToken", refreshToken, {
        httpOnly: false,
        sameSite: process.env.NODE_ENV === "production" ? "None" : "Lax",
        secure: process.env.NODE_ENV === "production",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 أيام
        path: "/",
        domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
      })
      .status(201)
      .json({
        message: "REGISTRATION SUCCESSFUL",
        user: {
          _id: saveData._id,
          name: saveData.name,
          email: saveData.email
        }
      });
  } catch (error) {
    res.status(500).json({
      message: error.message,
    });
  }
});

auth.post("/login", async (req, res) => {
  try {
    console.log("Login attempt with data:", req.body);
    const { email1, password1 } = req.body;
    console.log("Email:", email1, "Password:", password1);

    // تعطيل التحقق من وجود ملفات تعريف الارتباط مؤقتًا
    // const availableAccessToken = req.cookies?.accessToken;
    // const availableRefreshToken = req.cookies?.refreshToken;
    // console.log("Available tokens:", { accessToken: availableAccessToken, refreshToken: availableRefreshToken });

    // if (availableAccessToken || availableRefreshToken) {
    //   console.log("User already logged in");
    //   return res.status(400).json({
    //     message: "You are already logged in",
    //   });
    // }

    // مسح ملفات تعريف الارتباط الحالية إذا وجدت
    if (req.cookies?.accessToken) {
      // Limpiar con configuración None
      res.clearCookie("accessToken", {
        httpOnly: false,
        sameSite: "None",
        secure: false,
        path: "/"
      });
      // Limpiar también con configuración Lax
      res.clearCookie("accessToken", {
        httpOnly: false,
        sameSite: "Lax",
        secure: false,
        path: "/"
      });
    }
    if (req.cookies?.refreshToken) {
      // Limpiar con configuración None
      res.clearCookie("refreshToken", {
        httpOnly: false,
        sameSite: "None",
        secure: false,
        path: "/"
      });
      // Limpiar también con configuración Lax
      res.clearCookie("refreshToken", {
        httpOnly: false,
        sameSite: "Lax",
        secure: false,
        path: "/"
      });
    }

    console.log("Looking for user with email:", email1);
    const user = await userData.findOne({ email: email1 });
    console.log("User found:", user ? "Yes" : "No");

    if (!user) {
      console.log("User doesn't exist");
      return res.status(404).json({
        message: "USER DOESN'T EXIST",
      });
    }

    const userPassword = user.password;
    console.log("Comparing passwords...");
    const checkPassword = await bcrypt.compare(password1, userPassword);
    console.log("Password match:", checkPassword);

    if (checkPassword) {
      const refreshToken = req.cookies?.refreshToken;
      console.log("Existing refresh token:", refreshToken);

      if (!refreshToken) {
        console.log("Creating new tokens...");
        const newRefreshToken = generateRefreshToken(user);
        const accessToken = generateAccessToken(user);
        console.log("New tokens created:", { accessToken, refreshToken: newRefreshToken });

        res
          .cookie("refreshToken", newRefreshToken, {
            httpOnly: false,
            sameSite: process.env.NODE_ENV === "production" ? "None" : "Lax",
            secure: process.env.NODE_ENV === "production",
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 أيام
            path: "/",
            domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
          })
          .cookie("accessToken", accessToken, {
            httpOnly: false,
            sameSite: process.env.NODE_ENV === "production" ? "None" : "Lax",
            secure: process.env.NODE_ENV === "production",
            maxAge: 24 * 60 * 60 * 1000, // 24 ساعة
            path: "/",
            domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
          });
        console.log("Cookies set");

        user.refreshToken = newRefreshToken;
        await user.save();
        console.log("User saved with new refresh token");
      } else {
        console.log("Creating new access token only...");
        const accessToken = generateAccessToken(user);
        console.log("New access token:", accessToken);

        res.cookie("accessToken", accessToken, {
          httpOnly: false,
          sameSite: process.env.NODE_ENV === "production" ? "None" : "Lax",
          secure: process.env.NODE_ENV === "production",
          maxAge: 24 * 60 * 60 * 1000, // 24 ساعة
          path: "/",
          domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
        });
        console.log("Access token cookie set");
      }

      console.log("Login successful, sending response");
      return res.status(200).json({
        message: "LOGIN SUCCESSFUL",
      });
    } else {
      console.log("Invalid credentials");
      res.status(401).json({
        message: "INVALID CREDENTIALS",
      });
    }
  } catch (error) {
    res.status(500).json({
      message: error.message,
    });
  }
});

auth.post("/resetlink", async (req, res) => {
  try {
    const { email } = req.body;
    const user = await userData.findOne({ email });

    if (!user) {
      return res.status(404).json({
        message: "USER DOESN'T EXIST",
      });
    }

    // استخدام قيمة ثابتة للمفتاح السري في حالة عدم وجود متغير البيئة
    const SECRET_KEY = process.env.SECRET_KEY || "toika_369_secret_key_for_jwt_tokens";
    const resetToken = jwt.sign({ email }, SECRET_KEY, {
      expiresIn: "30m",
    });
    const resetLink = `${process.env.BACKEND_URL}/${user?._id}/${resetToken}`;

    // تعطيل إرسال البريد الإلكتروني مؤقتًا
    console.log("Email sending disabled temporarily");

    // إرسال استجابة نجاح
    res.status(200).json({
      message: "Password reset link sent to your email",
    });
  } catch (error) {
    res.status(500).json({
      message: error.message,
    });
  }
});

auth.get("/logout", async (req, res) => {
  try {
    const accessToken = req.cookies?.accessToken;
    const refreshToken = req.cookies?.refreshToken;

    console.log("Logout attempt - tokens found:", { accessToken: !!accessToken, refreshToken: !!refreshToken });

    // مسح الكوكيز بجميع الإعدادات المحتملة
    const cookieOptions = [
      {
        httpOnly: false,
        sameSite: "None",
        secure: process.env.NODE_ENV === "production",
        path: "/",
        domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
      },
      {
        httpOnly: false,
        sameSite: "Lax",
        secure: false,
        path: "/",
        domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
      },
      {
        httpOnly: false,
        sameSite: "None",
        secure: false,
        path: "/"
      },
      {
        httpOnly: false,
        sameSite: "Lax",
        secure: false,
        path: "/"
      }
    ];

    // مسح كوكيز accessToken بجميع الإعدادات
    cookieOptions.forEach(options => {
      res.clearCookie("accessToken", options);
      res.clearCookie("refreshToken", options);
    });

    // إذا كان هناك refreshToken، قم بإزالته من قاعدة البيانات
    if (refreshToken) {
      try {
        const { verifyRefreshToken } = require("../lib/tokens");
        const decoded = verifyRefreshToken(refreshToken);
        if (decoded && decoded.id) {
          const user = await userData.findById(decoded.id);
          if (user) {
            user.refreshToken = null;
            await user.save();
            console.log("Refresh token removed from database");
          }
        }
      } catch (tokenError) {
        console.log("Error removing refresh token from database:", tokenError.message);
      }
    }

    console.log("Logout successful");
    res.status(200).json({
      success: true,
      message: "تم تسجيل الخروج بنجاح",
    });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
});

auth.get("/userdata", async (req, res) => {
  try {
    console.log("Fetching user data...");
    const accessToken = req.cookies?.accessToken;
    const refreshToken = req.cookies?.refreshToken;
    console.log("Cookies:", { accessToken, refreshToken });

    // تحقق من وجود التوكن في الكوكيز
    if (!accessToken && !refreshToken) {
      console.log("No tokens found, user not logged in");
      return res.status(400).json({
        success: false,
        message: "You are not logged in",
      });
    }

    // محاولة التحقق من توكن الوصول إذا كان موجودًا
    if (accessToken) {
      try {
        console.log("Verifying access token...");
        const userdata = verifyAccessToken(accessToken);
        console.log("Token verified, user ID:", userdata?.id);

        const user = await userData.findById(userdata?.id).select("-password");
        console.log("User found:", user ? "Yes" : "No");

        if (!user) {
          console.log("User not found in database");
          return res.status(404).json({
            success: false,
            message: "User not found",
          });
        }

        // تجديد التوكن لضمان استمرار الجلسة
        const newAccessToken = generateAccessToken(user);
        res.cookie("accessToken", newAccessToken, {
          httpOnly: false,
          sameSite: process.env.NODE_ENV === "production" ? "None" : "Lax",
          secure: process.env.NODE_ENV === "production",
          maxAge: 24 * 60 * 60 * 1000, // 24 ساعة
          path: "/",
          domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
        });

        console.log("Sending user data");
        return res.status(200).json({
          success: true,
          user,
        });
      } catch (tokenError) {
        console.log("Access token verification failed:", tokenError.message);
        // سنستمر للتحقق من توكن التحديث
      }
    }

    // محاولة التحقق من توكن التحديث إذا كان موجودًا
    if (refreshToken) {
      try {
        console.log("Trying to refresh token...");
        const refreshData = verifyRefreshToken(refreshToken);
        console.log("Refresh token verified, user ID:", refreshData?.id);

        const user = await userData.findById(refreshData?.id).select("-password");
        console.log("User found from refresh token:", user ? "Yes" : "No");

        if (!user) {
          console.log("User not found in database");
          return res.status(404).json({
            success: false,
            message: "User not found",
          });
        }

        // إنشاء توكن وصول جديد
        const newAccessToken = generateAccessToken(user);
        console.log("Generated new access token");

        // تعيين كوكي توكن الوصول الجديد
        res.cookie("accessToken", newAccessToken, {
          httpOnly: false,
          sameSite: process.env.NODE_ENV === "production" ? "None" : "Lax",
          secure: process.env.NODE_ENV === "production",
          maxAge: 24 * 60 * 60 * 1000, // 24 ساعة
          path: "/",
          domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
        });

        // تجديد توكن التحديث أيضًا
        const newRefreshToken = generateRefreshToken(user);
        res.cookie("refreshToken", newRefreshToken, {
          httpOnly: false,
          sameSite: process.env.NODE_ENV === "production" ? "None" : "Lax",
          secure: process.env.NODE_ENV === "production",
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7 أيام
          path: "/",
          domain: process.env.NODE_ENV === "production" ? undefined : "localhost"
        });

        console.log("Sending user data with new tokens");
        return res.status(200).json({
          success: true,
          user,
        });
      } catch (refreshError) {
        console.log("Refresh token verification failed:", refreshError.message);
        // مسح الكوكيز غير الصالحة
        res.clearCookie("accessToken", {
          httpOnly: false,
          sameSite: "None",
          secure: false,
          path: "/"
        });
        res.clearCookie("refreshToken", {
          httpOnly: false,
          sameSite: "None",
          secure: false,
          path: "/"
        });

        return res.status(401).json({
          success: false,
          message: "Invalid tokens, please login again",
        });
      }
    }

    // إذا وصلنا إلى هنا، فهذا يعني أن التوكن موجود ولكنه غير صالح
    return res.status(401).json({
      success: false,
      message: "Invalid tokens, please login again",
    });
  } catch (error) {
    console.log("Error in userdata endpoint:", error.message);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
});

module.exports = auth;
