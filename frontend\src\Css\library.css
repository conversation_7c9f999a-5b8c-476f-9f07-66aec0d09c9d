.library-section {
  display: flex;
  flex-direction: column;
  position: absolute;
  width: -webkit-fill-available;
  top: 100px;
  color: white;
  padding-right: 55px;
}

.top-watchlater-library {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.top-watchlater-library > p {
  margin-left: 15px;
}

.top-watchlater-library > p:nth-child(3) {
  margin-left: 15px;
  color: #aaa;
}

.topplaylist-section > p {
  margin-left: 15px;
}

.thiswatch-thumbnail {
  width: 230px;
  border-radius: 6px;
}

.watchlater-library-videos {
  display: flex;
  margin-top: 40px;
  overflow: scroll;
  scroll-behavior: smooth;
}

.watchlater-library-videos2 {
  margin-top: 40px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  /* grid-template-columns: repeat(5, 1fr); */
  /* grid-template-rows: repeat(4, 1fr); */
  justify-content: center;
  grid-column-gap: 10px;
  grid-row-gap: 18px;
}

.thislibrary-duration {
  width: fit-content;
  position: relative;
  bottom: 33px;
  left: 190px;
  background-color: #000000c9;
  padding: 5px;
  border-radius: 4px;
  font-size: 12.5px;
}

.thislibrary-video-details {
  position: relative;
  font-size: 14.6px;
  bottom: 20px;
}

.thislibrary-video-details > p {
  width: 90%;
}

.see-all2 {
  cursor: pointer;
  color: #3ea6ff;
  padding: 8px;
  padding-left: 12px;
  padding-right: 12px;
  transition: all 0.15s ease;
  border-radius: 24px;
  position: absolute;
  right: 25px;
}

.see-all {
  cursor: pointer;
  color: #3ea6ff;
  padding: 8px;
  padding-left: 12px;
  padding-right: 12px;
  transition: all 0.15s ease;
  border-radius: 24px;
}

.see-all:hover,
.see-all2:hover {
  background-color: #0659a157;
}

.thisvide-oneliner-1 {
  font-size: 13.2px;
  color: #aaa;
  margin-top: 3px;
  display: flex;
  align-items: center;
}

.thisvide-oneliner-2 {
  font-size: 13.2px;
  color: #aaa;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.thiswatchlater-videoss {
  cursor: pointer;
  margin-right: 12px;
  width: min-content;
}

.topplaylist-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.playy-all-btn2 {
  color: white;
  position: absolute;
  display: flex;
  align-items: center;
  padding-left: 60px;
  bottom: 200px;
  transition: all 0.15s ease;
  opacity: 0;
}

.created-all-playlistss2 {
  margin-right: 12px;
  width: min-content;
  cursor: pointer;
  transition: all 0.15s ease;
}

.thischannel-playlists2 {
  display: flex;
  margin-top: 40px;
  overflow: scroll;
}

.view-playlist,
.view-playlist2 {
  margin-top: 14px;
  font-size: 13px;
  color: #aaa;
  transition: all 0.15s ease;
}

.playlist-ownner,
.owner-light {
  margin-top: 10px !important;
  font-size: 12.5px;
  color: #aaa;
  transition: all 0.15s ease;
}

.playlist-ownner:hover {
  color: white;
}

.owner-light:hover {
  color: black;
}

.view-playlist:hover {
  color: white;
}

.view-playlist2:hover {
  color: black !important;
}

.private-privacyy {
  display: flex;
  align-items: center;
  background-color: #272727;
  padding: 3.5px;
  width: fit-content;
  padding-left: 4.5px;
  padding-right: 4.5px;
  margin-top: 8px;
  border-radius: 4px;
}

.private-privacyy > p {
  margin-left: 5px;
  font-size: 13.5px;
  color: #aaa;
}

.private-privacyy-light {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  padding: 3.5px;
  width: fit-content;
  padding-left: 4.5px;
  padding-right: 4.5px;
  margin-top: 8px;
  border-radius: 4px;
}

.private-privacyy-light > p {
  margin-left: 5px;
  font-size: 13.5px;
  color: rgb(31, 31, 31);
}

.extra-playlists-data {
  display: flex;
  align-items: start;
  flex-direction: column;
}

.playlists-library,
.likedvideos-library {
  margin-top: 8px;
}

.top-like-lefttt,
.top-watch-left {
  display: flex;
  align-items: center;
  width: 150px;
  justify-content: space-between;
}
.playlistt-left {
  display: flex;
  align-items: center;
  width: 120px;
  justify-content: space-between;
}

@media (width <= 860px) {
  .library-section {
    padding-right: 25px !important;
    left: 120px !important;
  }
}

@media (width <= 650px) {
  .library-section {
    left: 25px !important;
    top: 75px !important;
    padding-bottom: 90px !important;
  }
}

@media (width <= 450px) {
  .thiswatch-thumbnail {
    width: 190px;
  }
  .thislibrary-duration {
    left: 150px;
    font-size: 11.5px;
  }
  .thislibrary-video-details {
    font-size: 13.4px;
  }
  .thisvide-oneliner-1,
  .thisvide-oneliner-2 {
    font-size: 12px;
  }
  .top-like-lefttt,
  .top-watch-left {
    width: 145px;
    font-size: 15px;
  }
  .playlist-main-img {
    width: 190px;
    height: 110px;
  }
  .playlist-element {
    font-size: 11.5px;
  }
  .watchlater-library-videos,
  .thischannel-playlists2 {
    margin-top: 25px;
  }
  .playlistt-details {
    font-size: 13.8px;
  }
  .playlist-ownner {
    font-size: 12px;
  }
  .privateone {
    font-size: large !important;
  }
  .private-privacyy > p {
    font-size: 12px;
  }
  .playlistt-left {
    font-size: 15px;
  }
}

@media (width <= 400px) {
  .library-section {
    padding-bottom: 90px !important;
  }
}
