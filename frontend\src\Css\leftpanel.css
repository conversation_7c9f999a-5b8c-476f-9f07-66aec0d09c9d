.main-left-section {
  background-color: #0f0f0f;
  color: white;
  position: fixed;
  top: 60px;
  height: 100%;
  padding: 16px;
  width: 225px;
  line-height: 48px;
  z-index: 1;
  height: -webkit-fill-available;
  overflow: scroll;
}

.changeBG {
  background-color: #3d3f4365;
  border-radius: 8px;
}

.changeBG-light {
  background-color: #f0f0f0;

  border-radius: 8px;
}

.sec-data {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-left: 10px;
  padding-right: 80px;
  transition: all 0.15s ease;
}

.sec-data:hover {
  background-color: #3d3f4365;
  border-radius: 8px;
}

.sec-data > p {
  padding-left: 20px;
}

.seperate {
  border: 1px solid #5b5d6265 !important;
  margin-top: 15px;
  margin-bottom: 15px;
}

.seperate-light {
  border: 1px solid #bfbfbf65 !important;
  margin-top: 15px;
  margin-bottom: 15px;
}

/* SHORT HAND  */

.sec-data2 {
  padding: 12px !important;
  display: flex;
}

.main-2 {
  width: 62px !important;
  flex-direction: column;
  align-items: center;
}

.trending2,
.subscription2,
.library2,
.watch-later2,
.liked-video2 {
  margin-top: 15px;
}

.channel-profilee {
  width: 35px;
  height: 35px;
  object-fit: cover;
  border-radius: 100%;
}

.mysubscriptions {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-left: 10px;
  padding-right: 80px;
  transition: all 0.15s ease;
}

.mysubscriptions:hover {
  background-color: #3d3f4365;
  border-radius: 8px;
}

.sub-channelnamee {
  padding-left: 20px;
}

.my-playlist-data {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-left: 10px;
  padding-right: 80px;
  transition: all 0.15s ease;
}

.my-playlist-data:hover {
  background-color: #3d3f4365;
  border-radius: 8px;
}

.my-playlist-data > p {
  padding-left: 20px;
}

.developer {
  margin-top: 25px;
  margin-left: 10px;
  line-height: normal !important;
  font-size: 15px;
  display: flex;
  padding-bottom: 26px;
  align-items: center;
}
.developer > a {
  margin-left: 10px;
  color: #3eaffe;
  text-decoration: none;
}

.secondary-panel {
  position: fixed;
  background-color: #0f0f0f;
  z-index: 11;
  height: 100%;
  overflow: scroll;
  animation: COME_IN 0.35s both;
}

@keyframes COME_IN {
  0% {
    left: -300px;
  }
  100% {
    left: 0px;
  }
}

.moveout {
  animation: COME_OUT 0.35s both;
}

@keyframes COME_OUT {
  0% {
    left: 0px;
  }
  100% {
    left: -300px;
  }
}

.main-left-section-new {
  background-color: #0f0f0f;
  color: white;
  height: 100vh;
  padding: 16px;
  width: 225px;
  line-height: 48px;
  position: relative;
  top: 70px;
}

.panel-topdata {
  padding: 20px;
  position: fixed;
  z-index: 1;
  width: 215px;
  background-color: #0f0f0f;
}

.close-sidepannel {
  transition: all 0.15s ease;
}

.close-sidepannel:hover {
  color: white !important;
  background-color: #3d3f4365;
}

.main-3 {
  z-index: 5;
}

.horizontal-panel {
  display: none;
  background-color: #0f0f0f;
  position: fixed;
  z-index: 6;
  bottom: 0;
  padding: 8px;
  padding-left: 22px;
  padding-right: 22px;
  padding-top: 10px;
  width: -webkit-fill-available;
  border-top: 2px solid rgba(128, 128, 128, 0.333);
}

.horizontal-main-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.hori {
  text-align: center;
  color: white;
  font-size: 13px;
  line-height: 20px;
}

/* MEDIA QUERIES */
@media (max-width: 1100px) {
  .main-left-section {
    font-size: 15px;
  }
}
@media (max-width: 860px) {
  .main-left-section {
    display: none !important;
  }
  .main-3 {
    display: flex !important;
    margin-top: 8px;
  }
  .main-left-section-new {
    font-size: 15px;
  }
}

@media (max-width: 650px) {
  .main-left-section {
    display: none !important;
  }
  .horizontal-panel {
    display: block !important;
  }
}

@media (max-width: 450px) {
  .hor-icons {
    font-size: 24px !important;
  }
  .addvid-icon {
    font-size: 44px !important;
  }
}

@media (width < 355px) {
  .hor-icons {
    width: 22px !important;
  }
  .hori > p {
    font-size: 12px !important;
  }
  .horizontal-panel {
    padding-left: 15px;
    padding-right: 15px;
  }
}
