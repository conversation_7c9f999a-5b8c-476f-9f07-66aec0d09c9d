import { useEffect, useState } from "react";
import "../Css/accountPop.css";
import avatar from "../img/avatar.png";
import AccountBoxOutlinedIcon from "@mui/icons-material/AccountBoxOutlined";
import DarkModeOutlinedIcon from "@mui/icons-material/DarkModeOutlined";
import ArrowForwardIosRoundedIcon from "@mui/icons-material/ArrowForwardIosRounded";
import TranslateOutlinedIcon from "@mui/icons-material/TranslateOutlined";
import LogoutOutlinedIcon from "@mui/icons-material/LogoutOutlined";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import DoneOutlinedIcon from "@mui/icons-material/DoneOutlined";
import Tooltip from "@mui/material/Tooltip";
import Zoom from "@mui/material/Zoom";
import { SiYoutubestudio } from "react-icons/si";
import { useSelector } from "react-redux";

function AccountPop() {
  // const backendURL = "https://youtube-clone-mern-backend.vercel.app"
  const backendURL = "http://localhost:3000";
  const [profile, setProfile] = useState("");
  const [theme, setTheme] = useState(() => {
    const Dark = localStorage.getItem("Dark");
    return Dark ? JSON.parse(Dark) : true;
  });
  const [ChannelID, setChannelID] = useState();
  const [isBtnClicked, setIsBtnClicked] = useState(false);
  const [isChannel, setIsChannel] = useState(false);

  useEffect(() => {
    localStorage.setItem("Dark", JSON.stringify(theme));
  }, [theme]);

  const User = useSelector((state) => state.user.user);
  const { user } = User;

  useEffect(() => {
    const getUserData = async () => {
      try {
        if (user?.email) {
          const response = await fetch(
            `${backendURL}/getuserimage/${user?.email}`
          );
          const { channelIMG } = await response.json();
          setProfile(channelIMG);
        }
      } catch (error) {}
    };

    getUserData();
  }, [user?.email]);

  useEffect(() => {
    const getChannelID = async () => {
      try {
        if (user?.email) {
          const response = await fetch(
            `${backendURL}/getchannelid/${user?.email}`
          );
          const { channelID } = await response.json();
          setChannelID(channelID);
        }
      } catch (error) {}
    };

    getChannelID();
  }, [user?.email]);

  useEffect(() => {
    const getChannel = async () => {
      try {
        if (user?.email) {
          const response = await fetch(
            `${backendURL}/getchannel/${user?.email}`
          );
          const { hasChannel } = await response.json();
          setIsChannel(hasChannel);
        }
      } catch (error) {}
    };

    getChannel();
  }, [user?.email]);

  const handleLogout = async () => {
    try {
      console.log("Logging out...");

      // أولاً، مسح البيانات المحلية
      localStorage.removeItem("userData");
      localStorage.removeItem("isLoggedIn");
      localStorage.removeItem("userId");
      localStorage.removeItem("userEmail");
      localStorage.removeItem("loginTime");

      const response = await fetch(`${backendURL}/logout`, {
        credentials: "include",
        headers: {
          "Accept": "application/json",
        },
      });

      console.log("Logout response:", response);

      if (response.ok) {
        const data = await response.json();
        console.log("Logout data:", data);

        if (data.success) {
          console.log("Server logout successful");
        }
      }

      // تنظيف ملفات تعريف الارتباط يدويًا بجميع الطرق الممكنة
      const cookieCleanupOptions = [
        "; path=/",
        "; path=/; domain=localhost",
        "; path=/; domain=.localhost",
        "; path=/; secure",
        "; path=/; secure; samesite=none",
        "; path=/; samesite=lax"
      ];

      cookieCleanupOptions.forEach(options => {
        document.cookie = `accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC${options}`;
        document.cookie = `refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC${options}`;
      });

      // إعادة تحميل الصفحة بعد تأخير قصير للتأكد من مسح البيانات
      setTimeout(() => {
        console.log("Redirecting to home page...");
        window.location.href = "/";
      }, 500);

    } catch (error) {
      console.log("Logout error:", error);

      // في حالة حدوث خطأ، نحاول تنظيف البيانات على أي حال
      localStorage.removeItem("userData");
      localStorage.removeItem("isLoggedIn");
      localStorage.removeItem("userId");
      localStorage.removeItem("userEmail");
      localStorage.removeItem("loginTime");

      // تنظيف ملفات تعريف الارتباط
      const cookieCleanupOptions = [
        "; path=/",
        "; path=/; domain=localhost",
        "; path=/; domain=.localhost",
        "; path=/; secure",
        "; path=/; secure; samesite=none",
        "; path=/; samesite=lax"
      ];

      cookieCleanupOptions.forEach(options => {
        document.cookie = `accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC${options}`;
        document.cookie = `refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC${options}`;
      });

      setTimeout(() => {
        window.location.href = "/";
      }, 500);
    }
  };

  return (
    <>
      <div
        className={
          theme ? "account-pop" : "account-pop account-pop-light light-mode"
        }
        style={
          isBtnClicked === false ? { display: "block" } : { display: "none" }
        }
      >
        <div className="user-section">
          <div className="left-part">
            <img
              src={profile ? profile : avatar}
              alt="channelIMG"
              className="channelIMG"
            />
          </div>
          <div className="right-part">
            <p>{user?.name}</p>
            <Tooltip
              TransitionComponent={Zoom}
              title={user?.email}
              placement="bottom"
            >
              <p>
                {user?.email.slice(0, 12)}
                {user?.email.length > 12 ? "..." : ""}
              </p>
            </Tooltip>
          </div>
        </div>
        <hr className={theme ? "seperate" : "seperate-light"} />
        <div className="about-channel-section">
          <div
            className={theme ? "yourchannel c-sec" : "yourchannel c-sec2"}
            onClick={() => {
              if (isChannel === true) {
                window.location.href = `/channel/${ChannelID}`;
              } else {
                window.location.href = `/studio`;
              }
            }}
          >
            <AccountBoxOutlinedIcon
              fontSize="medium"
              style={{ color: theme ? "white" : "black" }}
            />
            <p>قناتك</p>
          </div>
          <div
            className={theme ? "yourstudio c-sec" : "yourstudio c-sec2"}
            onClick={() => {
              window.location.href = "/studio";
            }}
          >
            <SiYoutubestudio
              fontSize="21px"
              style={{ color: theme ? "white" : "black" }}
            />
            <p>استوديو aflamNET</p>
          </div>
          <div
            className={theme ? "apperance c-sec" : "apperance c-sec2"}
            onClick={() => {
              if (isBtnClicked === false) {
                setIsBtnClicked(true);
              } else {
                setIsBtnClicked(false);
              }
            }}
          >
            <DarkModeOutlinedIcon
              fontSize="medium"
              style={{ color: theme ? "white" : "black" }}
            />
            <p>المظهر: {theme ? "داكن" : "فاتح"}</p>
            <ArrowForwardIosRoundedIcon
              className="open"
              fontSize="small"
              style={{ color: theme ? "#ffffff8a" : "black" }}
            />
          </div>
        </div>
        <hr className={theme ? "seperate" : "seperate-light"} />
        <div className="extra1-section">
          <div className={theme ? "language c-sec" : "language c-sec2"}>
            <TranslateOutlinedIcon
              fontSize="medium"
              style={{ color: theme ? "white" : "black" }}
            />
            <p>اللغة: العربية</p>
          </div>
          <div
            className={theme ? "exitout c-sec" : "exitout c-sec2"}
            onClick={handleLogout}
          >
            <LogoutOutlinedIcon
              fontSize="medium"
              style={{ color: theme ? "white" : "black" }}
            />
            <p>تسجيل الخروج</p>
          </div>
        </div>
      </div>
      <div
        className={
          theme ? "account-pop" : "account-pop account-pop-light light-mode"
        }
        style={
          isBtnClicked === true
            ? { display: "block", paddingTop: "12px" }
            : { display: "none", paddingTop: "20px" }
        }
      >
        <div className="appearance-title">
          <ArrowBackOutlinedIcon
            className={theme ? "back-arrow" : "back-arroww2"}
            fontSize="medium"
            style={{ color: theme ? "white" : "black" }}
            onClick={() => {
              if (isBtnClicked === true) {
                setIsBtnClicked(false);
              } else {
                setIsBtnClicked(true);
              }
            }}
          />
          <p>المظهر</p>
        </div>
        <hr
          className={theme ? "seperate" : "seperate-light"}
          style={
            isBtnClicked === true ? { marginTop: "6px" } : { marginTop: "15px" }
          }
        />
        <div className="theme-section">
          <p className="caution">الإعدادات تطبق على هذا المتصفح فقط</p>
          <div className="theme-list">
            <div
              className={theme ? "dark-theme" : "dark-theme2"}
              onClick={() => {
                setTheme(true);
                window.location.reload();
              }}
            >
              <DoneOutlinedIcon
                className="dark-arrow"
                fontSize="medium"
                color={theme ? "white" : "black"}
                style={theme === true ? { opacity: 1 } : { opacity: 0 }}
              />
              <p>مظهر داكن</p>
            </div>
            <div
              className={theme ? "light-theme" : "light-theme2"}
              onClick={() => {
                setTheme(false);
                window.location.reload();
              }}
            >
              <DoneOutlinedIcon
                className="light-arrow"
                fontSize="medium"
                color={theme ? "white" : "black"}
                style={theme === false ? { opacity: 1 } : { opacity: 0 }}
              />
              <p>مظهر فاتح</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default AccountPop;
