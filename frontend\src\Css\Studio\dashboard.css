@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@600&display=swap");

.dashboard-data {
  position: absolute;
  top: 100px;
  left: 310px;
  width: -webkit-fill-available;
  animation: FADEINN 0.45s ease;
}

.dashboard-top {
  color: white;
  font-family: "Open Sans", sans-serif;
  font-weight: 700;
  font-size: 24px;
}

.dashboard-performance {
  background-color: #282828;
  padding: 25px;
  position: relative;
  top: 25px;
  width: fit-content;
  color: white;
  border: 1px solid rgba(128, 128, 128, 0.333);
  border-radius: 5px;
}

.performed-vid-data {
  margin-top: 20px;
}

.video-performance-icons,
.performed-video-views,
.left-performed-icons,
.performed-video-comments,
.performed-video-likes {
  display: flex;
  align-items: center;
}

.video-performance > p:nth-child(1) {
  font-size: 17px;
}

.video-performance-icons {
  justify-content: space-between;
  padding-top: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(128, 128, 128, 0.333);
}

.left-performed-icons {
  display: flex;
  align-items: center;
  width: 45%;
  justify-content: space-between;
}
.performed-video-views > p,
.performed-video-comments > p,
.performed-video-likes > p {
  font-size: 13px;
  margin-left: 8px;
  color: #aaa;
}

.see-comments-dash {
  cursor: pointer;
  margin-top: 15px;
  font-size: 15px;
  color: #3eaffe;
  padding: 10px;
  transition: all 0.2s ease;
  width: fit-content;
  position: relative;
  right: 10px;
}

.see-comments-dash:hover {
  background-color: #3eaefe36;
}

.views-ranking,
.thisviews-performed {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13.5px;
}

.total-outof {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.extra-performance-data {
  margin-top: 15px;
  line-height: 35px;
}

.extra-performance-data > p {
  font-size: 13.5px;
  color: #aaa;
}

.expandd {
  padding: 5px;
  border-radius: 100%;
  transition: all 0.5s ease !important;
}

.expandd:hover {
  color: white !important;
}

.expand-light:hover {
  color: #606060 !important;
}

.expandd:active {
  background-color: rgba(255, 255, 255, 0.276);
}

.all-sortedvideos-dash {
  opacity: 0;
  background-color: #222222;
  padding: 20px;
  position: absolute;
  z-index: 0;
  left: 420px;
  top: 43%;
  color: white;
  width: 420px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
  transition: opacity 0.15s ease;
}

.all-sortedvideos-dash > p:nth-child(1) {
  font-size: 15px;
}
.all-sortedvideos-dash > p:nth-child(2) {
  color: #aaa;
  font-size: 13.5px;
  margin-bottom: 15px;
}

.sortedthumbnail {
  width: 60px;
}

.list-of-sortedvideos {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 48px;
  font-size: 13.5px;
}

.leftsort-list {
  display: flex;
  align-items: center;
}

.sorted-viddataaa {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.right-arrw {
  cursor: pointer;
  position: relative;
  left: 5px;
}

.right-arrw-light {
  cursor: pointer;
  position: relative;
  left: 5px;
}

.right-arrw:hover {
  color: white !important;
}

.right-arrw-light:hover {
  color: #606060 !important;
}

.all-sortedvideos-dash.visible {
  opacity: 1;
  z-index: 3;
}

.published-videos-dash {
  position: relative;
  top: 50px;
  width: auto;
  background-color: #282828;
  color: white;
  border: 1px solid rgba(128, 128, 128, 0.333);
  border-radius: 5px;
}

.publish-toop {
  font-size: 17px;
  padding: 25px 0 0 25px;
}

.publish-imgs {
  width: 80px;
}

.dashboard-publishvideos {
  cursor: pointer;
  display: flex;
  align-items: start;
  padding: 15px 25px 15px 25px;
  transition: all 0.15s ease;
}

.dashboard-publishvideos:hover {
  background-color: #1f1f1f;
}

.dashboard-publishvideos-light:hover {
  background-color: #e1e1e1 !important;

}

.dashboard-publishvideos:hover .bottompublish {
  display: none;
}

.dashboard-publishvideos:hover .bottompublish2 {
  display: flex;
}

.all-publishvid-data {
  margin-top: 20px;
}

.publish-rightdata {
  margin-left: 16px;
}

.toppublish {
  font-size: 13px;
}

.bottompublish,
.bottompublish2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 145px;
  margin-top: 8px;
}

.bottompublish2 {
  display: none;
}

.publishviews,
.publishcomments,
.publishlikes {
  display: flex;
  align-items: center;
  font-size: 13px;
}
.publishviews > p,
.publishcomments > p,
.publishlikes > p {
  margin-left: 5px;
}

.go-videos {
  margin-left: 25px;
  margin-bottom: 15px;
  margin-top: 8px;
}
.dash-data-all {
  display: flex;
  padding-bottom: 80px;
}

.dashboard-analytics {
  position: relative;
  top: 25px;
  left: 20px;
  width: fit-content;
  background-color: #282828;
  width: fit-content;
  color: white;
  border: 1px solid rgba(128, 128, 128, 0.333);
  border-radius: 5px;
  padding: 25px;
  width: 350px;
}

.subscriber-analytics,
.channel-summary {
  border-bottom: 1px solid rgba(128, 128, 128, 0.333);
}

.channel-summary {
  padding-bottom: 20px;
  margin-top: 20px;
}

.channel-top-videos-analytics {
  margin-top: 20px;
}

.dashboard-analytics > p {
  font-size: 19px;
}

.subscriber-analytics {
  margin-top: 18px;
  padding-bottom: 20px;
}

.subscriber-analytics > p:nth-child(2) {
  font-size: 34px;
  margin-top: 5px;
}

.subscriber-analytics > p:nth-child(1) {
  font-size: 13.5px;
}

.channel-totalviews-analytics,
.channel-totalvideos-analytics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12px;
  font-size: 13.5px;
}

.channel-top-videos-analytics > p:nth-child(2) {
  font-size: 13.5px;
  color: #aaa;
  margin-top: 4px;
}

.channel-top-videosdata {
  margin-top: 12px;
  line-height: 30px;
}

.thisanalytics-data {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13.8px;
  transition: all 0.15s ease;
}

.thisanalytics-data:hover {
  color: #1188dd;
}

.make-white:hover {
  color: white !important;
}

.make-white-light:hover {
  color: gray !important;
}

.uploadnew-video-dash {
  background-color: #282828;
  border: 1px solid rgba(128, 128, 128, 0.333);
  padding: 14px;
  position: relative;
  top: 25px;
  border-radius: 5px;
}

.dashed-dash {
  border: 1px dashed rgba(128, 128, 128, 0.333);
  padding-left: 55px;
  padding-right: 55px;
  padding-top: 100px;
  padding-bottom: 100px;
  border-radius: 3px;
  text-align: center;
}

.dash-dataimp > p {
  font-size: 14.5px;
  color: #aaa;
}

.noupload-img {
  width: 150px;
}

.dash-dataimp > button {
  border: none;
  outline: none;
  background-color: #3eaffe;
  color: #282828;
  padding: 10px;
  padding-left: 16px;
  padding-right: 16px;
  font-size: 15px;
  border-radius: 2px;
  margin-top: 25px;
}

/* MEDIA QUERIES */

@media (width<= 1200px) {
  .dash-data-all {
    flex-direction: column;
    width: fit-content;
  }
  .dashboard-analytics {
    top: 75px;
    left: 0px;
    width: inherit !important;
  }
  .all-sortedvideos-dash {
    width: -webkit-fill-available;
    left: 0px;
    top: 30%;
  }
  .sk-dash-third {
    margin-left: 0px !important;
  }
}

@media (width<= 860px) {
  .dashboard-data {
    left: 125px !important;
  }
}

@media (width<= 650px) {
  .left-dashboard-data,
  .sk-dash {
    position: relative;
    top: 10px;
  }
  .dashboard-data {
    left: 6% !important;
    margin-right: 20px;
  }
  .performed-vid-data > img {
    width: 100% !important;
    height: auto !important;
  }
  .dash-data-all {
    padding-bottom: 180px !important;
  }
  .left-performed-icons {
    width: 165px !important;
  }
}

@media (width<= 450px) {
  .dashboard-top {
    font-size: 21px;
  }
  .sk-dash-first,
  .sk-dash-second,
  .sk-dash-third {
    width: 88vw !important;
  }
  .sk-dash-title {
    width: 200px !important;
  }

}
