.account-pop {
  /* background-color: #282828; */
  background-color: #282828db;
  position: fixed;
  color: white;
  z-index: 20;
  right: 20px;
  top: 68px;
  padding-top: 20px;
  padding-bottom: 20px;
  border-radius: 10px;
  width: 280px;
  backdrop-filter: blur(8px);
  animation: slowdown 0.2s ease;
}
.account-pop-light {
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}
.account-pop2 {
  /* background-color: #282828; */
  background-color: #292929;
  position: fixed;
  color: white;
  z-index: 20;
  right: 20px;
  top: 68px;
  padding-top: 20px;
  padding-bottom: 20px;
  border-radius: 10px;
  width: 280px;
  animation: slowdown 0.2s ease;
  box-shadow: rgba(0, 0, 0, 0.079) 0px 10px 20px,
    rgba(0, 0, 0, 0.05) 0px 6px 6px;
}

@keyframes slowdown {
  0% {
    top: 35px;
    opacity: 0;
  }

  100% {
    top: 68px;
    opacity: 1;
  }
}

.channelIMG {
  width: 42px;
  height: 42px;
  object-fit: cover;
  border-radius: 100%;
}

.user-section {
  cursor: pointer;
  display: flex;
  padding-left: 35px;
}

.right-part {
  margin-left: 15px;
  line-height: 25px;
}

.c-sec {
  cursor: pointer;
  display: flex;
  align-items: center;
  line-height: 45px;
  padding-left: 28px;
  transition: all 0.15s ease;
}

.c-sec:hover {
  background-color: #3e3e3e;
}

.c-sec > p {
  margin-left: 28px;
}

.c-sec2 {
  cursor: pointer;
  display: flex;
  align-items: center;
  line-height: 45px;
  padding-left: 28px;
  transition: all 0.15s ease;
}

.c-sec2:hover {
  background-color: #f0f0f0;
}

.c-sec2 > p {
  margin-left: 28px;
}

.open {
  margin-left: 35px;
}

.appearance-title {
  display: flex;
  align-items: center;
  padding-left: 25px;
}

.appearance-title > p {
  margin-left: 25px;
}

.caution {
  font-size: 13px;
  color: gray;
  text-align: center;
}

.theme-list {
  line-height: 45px;
  margin-top: 15px;
}

.dark-theme,
.light-theme {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-left: 25px;
  transition: all 0.15s ease;
}

.dark-theme:hover,
.light-theme:hover {
  background-color: #3e3e3e;
}

.dark-theme > p,
.light-theme > p {
  margin-left: 35px;
}

.dark-theme2,
.light-theme2 {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-left: 25px;
  transition: all 0.15s ease;
}

.dark-theme2:hover,
.light-theme2:hover {
  background-color: #f0f0f0;
}

.dark-theme2 > p,
.light-theme2 > p {
  margin-left: 35px;
}

.back-arrow {
  cursor: pointer;
  transition: all 0.15s ease;
  padding: 7px;
  border-radius: 100%;
}

.back-arrow:hover {
  background-color: #3e3e3e;
}

.back-arroww2 {
  cursor: pointer;
  transition: all 0.15s ease;
  padding: 7px;
  border-radius: 100%;
}

.back-arroww2:hover {
  background-color: #f0f0f0;
}
