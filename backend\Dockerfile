# Use Node.js 18 LTS
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Expose port
EXPOSE 10000

# Set environment variables
ENV NODE_ENV=production
ENV DB_user=aryaspubg12345
ENV DB_password=Pi7TUDrYpm14NCQG
ENV DB_name=""
ENV SECRET_KEY=toika_369_secret_key_for_jwt_tokens
ENV EMAIL=<EMAIL>
ENV PASSWORD=your_app_password
ENV PORT=10000

# Start the application
CMD ["npm", "start"]
