import Browse from "./Components/Browse";
import Studio from "./Components/Studio";
import Error from "./Components/Error";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import VideoSection from "./Components/VideoSection";
import LikeVideos from "./Components/LikeVideos";
import WatchLater from "./Components/WatchLater";
import OtherChannel from "./Components/Channel/OtherChannel";
import Subscriptions from "./Components/Subscriptions";
import Trending from "./Components/Trending";
import SearchResults from "./Components/SearchResults";
import Playlists from "./Components/Playlists";
import Library from "./Components/Library";
import Customization from "./Components/Studio/Customization";
import Content from "./Components/Studio/Content";
import VideoDetails from "./Components/Studio/VideoDetails";
import Comments from "./Components/Studio/Comments";
import VideoComments from "./Components/Studio/VideoComments";
import { ToastContainer, toast } from "react-toastify";
import { Helmet } from "react-helmet";
import ytLogo from "./img/icon.png";
import { useSelector, useDispatch } from "react-redux";
import { fetchUserData } from "./reducer/user";
import "react-toastify/dist/ReactToastify.css";
import { useEffect, useState } from "react";

function App() {
  const User = useSelector((state) => state.user.user);
  const { user } = User;
  const userStatus = useSelector((state) => state.user.status);
  const userError = useSelector((state) => state.user.error);
  const [isLoading, setIsLoading] = useState(true);

  const dispatch = useDispatch();

  // التحقق من وجود بيانات المستخدم في التخزين المحلي
  useEffect(() => {
    const checkLocalStorage = () => {
      const isLoggedIn = localStorage.getItem("isLoggedIn");
      const userData = localStorage.getItem("userData");

      if (isLoggedIn === "true" && userData) {
        console.log("User is logged in according to localStorage");
        try {
          const parsedUserData = JSON.parse(userData);
          console.log("Cached user data found:", parsedUserData);
        } catch (error) {
          console.error("Error parsing cached user data:", error);
          // إذا كانت البيانات تالفة، امسحها
          localStorage.removeItem("userData");
          localStorage.removeItem("isLoggedIn");
        }
      } else if (isLoggedIn === "true" && !userData) {
        console.log("Login flag found but no user data, clearing login status");
        localStorage.removeItem("isLoggedIn");
      }
    };

    checkLocalStorage();
  }, []);

  // جلب بيانات المستخدم من الخادم
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const result = await dispatch(fetchUserData());

        // تحقق من نجاح جلب البيانات
        if (result.payload && result.payload.success) {
          console.log("User data fetched successfully");
        } else {
          console.log("Failed to fetch user data or user not logged in");
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching user data:", error);
        setIsLoading(false);
      }
    };

    fetchData();

    // إعادة محاولة جلب البيانات كل 10 دقائق للحفاظ على الجلسة نشطة
    const intervalId = setInterval(() => {
      const isLoggedIn = localStorage.getItem("isLoggedIn");
      if (isLoggedIn === "true") {
        console.log("Refreshing user session...");
        dispatch(fetchUserData());
      }
    }, 10 * 60 * 1000); // 10 دقائق بدلاً من 5

    return () => clearInterval(intervalId);
  }, [dispatch]);

  // مراقبة تغييرات حالة المستخدم
  useEffect(() => {
    if (userStatus === 'succeeded' && user && user.success) {
      console.log("User state updated successfully:", user.user);
    } else if (userStatus === 'failed') {
      console.log("User fetch failed:", userError);
      // في حالة فشل جلب بيانات المستخدم، تحقق من البيانات المحلية
      const localUserData = localStorage.getItem("userData");
      const isLoggedIn = localStorage.getItem("isLoggedIn");

      if (!localUserData || isLoggedIn !== "true") {
        // امسح جميع بيانات الجلسة إذا لم تكن صالحة
        localStorage.removeItem("userData");
        localStorage.removeItem("isLoggedIn");
        localStorage.removeItem("userId");
        localStorage.removeItem("userEmail");
      }
    }
  }, [userStatus, user, userError]);

  return (
    <>
      <ToastContainer
        position="bottom-center"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
      <BrowserRouter>
        <Helmet>
          <link rel="icon" type="image/x-icon" href={ytLogo} />
        </Helmet>
        <Routes>
          <Route path="/" element={<Browse />} />
          <Route path="/home" element={<Browse />} />
          <Route
            path="/studio"
            element={user ? <Studio /> : <Error />}
          />
          <Route
            path="/studio/customize"
            element={user ? <Customization /> : <Error />}
          />
          <Route
            path="/studio/video"
            element={user ? <Content /> : <Error />}
          />
          <Route
            path="/studio/comments"
            element={user ? <Comments /> : <Error />}
          />
          <Route
            path="/studio/video/edit/:id"
            element={user ? <VideoDetails /> : <Error />}
          />
          <Route
            path="/studio/video/comments/:id"
            element={user ? <VideoComments /> : <Error />}
          />
          <Route
            path="/likedVideos"
            element={user ? <LikeVideos /> : <Error />}
          />
          <Route
            path="/watchlater"
            element={user ? <WatchLater /> : <Error />}
          />

          <Route
            path="/library"
            element={user ? <Library /> : <Error />}
          />
          <Route path="/channel/:id" element={<OtherChannel />} />
          <Route path="/trending" element={<Trending />} />
          <Route path="/results/:data" element={<SearchResults />} />
          <Route path="/playlist/:id" element={<Playlists />} />
          <Route
            path="/subscriptions"
            element={user ? <Subscriptions /> : <Error />}
          />
          <Route path="/video/:id" element={<VideoSection />} />
          <Route path="/*" element={<Error />} />
        </Routes>
      </BrowserRouter>
    </>
  );
}

export default App;
