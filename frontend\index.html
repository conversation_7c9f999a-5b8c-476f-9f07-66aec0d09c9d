<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="src/img/icon.png" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&family=Geologica:wght@400;700;900&display=swap" rel="stylesheet">
    <title>aflamNET - منصة الأفلام العربية</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <script>
      const currentURL = window.location.href;
      const local = "http://localhost:5173";

      if (currentURL === `${local}/studio`) {
        document.body.style.backgroundColor = "#1f1f1f";
      } else if (currentURL === `${local}/studio/video`) {
        document.body.style.backgroundColor = "#282828";
      } else if (currentURL.includes(`/studio/comments`)) {
        document.body.style.backgroundColor = "#282828";
      } else if (currentURL.includes(`/studio/video/comments`)) {
        document.body.style.backgroundColor = "#282828";
      } else if (currentURL.includes(`/studio/video/edit`)) {
        document.body.style.backgroundColor = "#282828";
      } else if (currentURL.includes(`/studio/customize`)) {
        document.body.style.backgroundColor = "#282828";
      } else {
        document.body.style.backgroundColor = "#0f0f0f";
      }
    </script>
  </body>
</html>
