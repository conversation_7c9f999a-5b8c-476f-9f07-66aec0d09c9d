require("dotenv").config();
require("../Database/database");
const express = require("express");
const cors = require("cors");
const bodyParser = require("body-parser");
const router = express.Router();
const auth = require("./auth");
const Channel = require("./channel");
const Videos = require("./videos");
const Likes = require("./likes");
const Comments = require("./comments");
const Studio = require("./studio");

// Middlewares
router.use(
  cors({
    // السماح بالوصول من المصادر المحددة
    origin: function(origin, callback) {
      const allowedOrigins = [
        'https://aflamnet.vercel.app', // URL de tu frontend en producción
        'http://localhost:5173',
        'http://localhost:3000',
        'http://localhost:4000'
      ];

      // En desarrollo, permitir solicitudes sin origen (como Postman)
      if (!origin || allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        console.log('Origen bloqueado por CORS:', origin);
        callback(null, true); // Permitir todos los orígenes en desarrollo
      }
    },
    methods: ["POST", "GET", "PUT", "DELETE", "OPTIONS"],
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    exposedHeaders: ['set-cookie']
  })
);
router.use(bodyParser.json());
router.use(bodyParser.urlencoded({ extended: true }));
router.use(auth);
router.use(Channel);
router.use(Videos);
router.use(Likes);
router.use(Comments);
router.use(Studio);

module.exports = router;
