services:
  - type: web
    name: aflamnet-backend
    env: node
    buildCommand: cd backend && npm install
    startCommand: cd backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: DB_user
        value: aryaspubg12345
      - key: DB_password
        value: Pi7TUDrYpm14NCQG
      - key: DB_name
        value: ""
      - key: SECRET_KEY
        value: toika_369_secret_key_for_jwt_tokens
      - key: EMAIL
        value: <EMAIL>
      - key: PASSWORD
        value: your_app_password
      - key: PORT
        value: 10000
    plan: free
